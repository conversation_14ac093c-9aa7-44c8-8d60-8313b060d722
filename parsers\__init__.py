import asyncio
import aiohttp
import time
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
from bs4 import BeautifulSoup
import random

class BaseParser(ABC):
    """Базовый класс для парсеров фриланс-площадок"""
    
    def __init__(self, name: str, base_url: str, logger=None):
        self.name = name
        self.base_url = base_url
        self.logger = logger
        self.session = None
        self.last_request_time = 0
        self.min_delay = 1  # Минимальная задержка между запросами в секундах
        self.max_delay = 3  # Максимальная задержка
        
        # Заголовки для имитации браузера
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер - вход"""
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Асинхронный контекстный менеджер - выход"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, url: str, **kwargs) -> Optional[str]:
        """Выполняет HTTP-запрос с контролем скорости"""
        # Контроль скорости запросов
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_delay:
            delay = random.uniform(self.min_delay, self.max_delay)
            if self.logger:
                self.logger.log_rate_limit(self.name, delay)
            await asyncio.sleep(delay)
        
        try:
            async with self.session.get(url, **kwargs) as response:
                self.last_request_time = time.time()
                
                if response.status == 200:
                    content = await response.text()
                    return content
                elif response.status == 429:  # Too Many Requests
                    if self.logger:
                        self.logger.log_rate_limit(self.name, 60)
                    await asyncio.sleep(60)
                    return None
                else:
                    if self.logger:
                        self.logger.warning(f"HTTP {response.status} для {url}")
                    return None
                    
        except asyncio.TimeoutError:
            if self.logger:
                self.logger.log_network_error(url, "Timeout")
            return None
        except Exception as e:
            if self.logger:
                self.logger.log_network_error(url, e)
            return None
    
    def _parse_budget(self, budget_text: str) -> int:
        """Извлекает числовое значение бюджета из текста"""
        if not budget_text:
            return 0
        
        # Удаляем все символы кроме цифр и некоторых ключевых слов
        import re
        
        # Ищем числа в тексте
        numbers = re.findall(r'\d+(?:\s?\d+)*', budget_text.replace(' ', ''))
        
        if not numbers:
            return 0
        
        # Берем первое найденное число
        budget_str = numbers[0].replace(' ', '')
        
        try:
            budget = int(budget_str)
            
            # Обработка сокращений (к, тыс, млн)
            budget_lower = budget_text.lower()
            if 'к' in budget_lower or 'тыс' in budget_lower:
                budget *= 1000
            elif 'млн' in budget_lower or 'миллион' in budget_lower:
                budget *= 1000000
            
            return budget
        except ValueError:
            return 0
    
    def _clean_text(self, text: str) -> str:
        """Очищает текст от лишних символов и пробелов"""
        if not text:
            return ""
        
        # Удаляем лишние пробелы и переносы строк
        text = ' '.join(text.split())
        
        # Удаляем HTML-теги если они остались
        text = BeautifulSoup(text, 'html.parser').get_text()
        
        return text.strip()
    
    def _extract_project_id(self, url: str) -> str:
        """Извлекает ID проекта из URL"""
        if not url:
            return ""
        
        # Пытаемся найти числовой ID в URL
        import re
        matches = re.findall(r'/(\d+)(?:/|$|\?)', url)
        if matches:
            return matches[-1]  # Берем последний найденный ID
        
        # Если не нашли, используем хеш URL
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()[:8]
    
    @abstractmethod
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с площадки. Должен быть реализован в наследниках"""
        pass
    
    def log_info(self, message: str):
        """Логирует информационное сообщение"""
        if self.logger:
            self.logger.info(f"[{self.name}] {message}")
    
    def log_error(self, message: str, exc_info=None):
        """Логирует ошибку"""
        if self.logger:
            self.logger.error(f"[{self.name}] {message}", exc_info=exc_info)
    
    def log_warning(self, message: str):
        """Логирует предупреждение"""
        if self.logger:
            self.logger.warning(f"[{self.name}] {message}")


class ParserManager:
    """Менеджер для управления всеми парсерами"""
    
    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
        self.parsers = {}
        self._init_parsers()
    
    def _init_parsers(self):
        """Инициализирует все парсеры"""
        from .fl_ru import FlRuParser
        from .freelance_ru import FreelanceRuParser
        from .habr_freelance import HabrFreelanceParser
        from .kwork import KworkParser
        from .youdo import YouDoParser
        from .weblancer import WebLancerParser
        from .upwork import UpworkParser
        from .freelansim import FreelansimParser
        
        # Создаем экземпляры парсеров
        self.parsers = {
            'fl_ru': FlRuParser(logger=self.logger),
            'freelance_ru': FreelanceRuParser(logger=self.logger),
            'habr_freelance': HabrFreelanceParser(logger=self.logger),
            'kwork': KworkParser(logger=self.logger),
            'youdo': YouDoParser(logger=self.logger),
            'weblancer': WebLancerParser(logger=self.logger),
            'upwork': UpworkParser(logger=self.logger),
            'freelansim': FreelansimParser(logger=self.logger),
        }
    
    async def parse_all_platforms(self) -> List[Dict[str, Any]]:
        """Парсит все включенные платформы"""
        all_projects = []
        enabled_platforms = self.config.enabled_platforms
        
        if self.logger:
            self.logger.log_monitoring_cycle_start()
        
        # Создаем задачи для параллельного парсинга
        tasks = []
        for platform_name in enabled_platforms:
            if platform_name in self.parsers:
                parser = self.parsers[platform_name]
                task = asyncio.create_task(self._parse_platform(parser))
                tasks.append(task)
        
        # Ждем завершения всех задач
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Собираем результаты
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                platform_name = enabled_platforms[i] if i < len(enabled_platforms) else "unknown"
                if self.logger:
                    self.logger.log_parsing_error(platform_name, result)
            elif isinstance(result, list):
                all_projects.extend(result)
        
        if self.logger:
            self.logger.log_monitoring_cycle_end(len(all_projects), 0)
        
        return all_projects
    
    async def _parse_platform(self, parser: BaseParser) -> List[Dict[str, Any]]:
        """Парсит одну платформу"""
        try:
            async with parser:
                projects = await parser.parse_projects()
                if self.logger:
                    self.logger.info(f"Найдено {len(projects)} проектов на {parser.name}")
                return projects
        except Exception as e:
            if self.logger:
                self.logger.log_parsing_error(parser.name, e)
            return []
    
    def get_available_platforms(self) -> List[str]:
        """Возвращает список доступных платформ"""
        return list(self.parsers.keys())
    
    def is_platform_enabled(self, platform: str) -> bool:
        """Проверяет, включена ли платформа"""
        return platform in self.config.enabled_platforms
