import telebot
from telebot import types
from typing import Dict, Any, List
import asyncio
from datetime import datetime, timedelta

class BotHandlers:
    """Класс для обработки команд Telegram бота"""
    
    def __init__(self, config, database, filter_manager, parser_manager, logger):
        self.config = config
        self.database = database
        self.filter_manager = filter_manager
        self.parser_manager = parser_manager
        self.logger = logger

        # Создаем бота (отключаем прокси)
        import os
        import requests

        # Полностью отключаем прокси для requests
        session = requests.Session()
        session.proxies = {}
        session.trust_env = False

        # Временно отключаем прокси через переменные окружения
        old_proxies = {}
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']

        for var in proxy_vars:
            if var in os.environ:
                old_proxies[var] = os.environ[var]
                del os.environ[var]

        try:
            # Патчим telebot для использования нашей сессии
            import telebot.apihelper
            telebot.apihelper.SESSION = session

            self.bot = telebot.TeleBot(config.telegram_token)
        finally:
            # Восстанавливаем прокси если они были
            for var, value in old_proxies.items():
                os.environ[var] = value
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Настраивает обработчики команд"""
        
        @self.bot.message_handler(commands=['start'])
        def start_command(message):
            self._start_command(message)
        
        @self.bot.message_handler(commands=['help'])
        def help_command(message):
            self._help_command(message)
        
        @self.bot.message_handler(commands=['status'])
        def status_command(message):
            self._status_command(message)
        
        @self.bot.message_handler(commands=['check'])
        def check_command(message):
            self._check_command(message)
        
        @self.bot.message_handler(commands=['stats'])
        def stats_command(message):
            self._stats_command(message)
        
        @self.bot.message_handler(commands=['add_keyword'])
        def add_keyword_command(message):
            self._add_keyword_command(message)
        
        @self.bot.message_handler(commands=['remove_keyword'])
        def remove_keyword_command(message):
            self._remove_keyword_command(message)
        
        @self.bot.message_handler(commands=['keywords'])
        def keywords_command(message):
            self._keywords_command(message)
        
        @self.bot.message_handler(commands=['add_excluded'])
        def add_excluded_command(message):
            self._add_excluded_command(message)
        
        @self.bot.message_handler(commands=['remove_excluded'])
        def remove_excluded_command(message):
            self._remove_excluded_command(message)
        
        @self.bot.message_handler(commands=['excluded'])
        def excluded_command(message):
            self._excluded_command(message)
        
        @self.bot.message_handler(commands=['budget'])
        def budget_command(message):
            self._budget_command(message)

        @self.bot.message_handler(commands=['set_min_budget'])
        def set_min_budget_command(message):
            self._set_min_budget_command(message)

        @self.bot.message_handler(commands=['set_max_budget'])
        def set_max_budget_command(message):
            self._set_max_budget_command(message)

        @self.bot.message_handler(commands=['remove_max_budget'])
        def remove_max_budget_command(message):
            self._remove_max_budget_command(message)

        @self.bot.message_handler(commands=['budget_info'])
        def budget_info_command(message):
            self._budget_info_command(message)

        @self.bot.message_handler(commands=['platforms'])
        def platforms_command(message):
            self._platforms_command(message)
        
        @self.bot.message_handler(commands=['toggle'])
        def toggle_command(message):
            self._toggle_command(message)
        
        @self.bot.message_handler(commands=['recent'])
        def recent_command(message):
            self._recent_command(message)

        @self.bot.message_handler(commands=['config'])
        def config_command(message):
            self._config_command(message)

        @self.bot.message_handler(commands=['diagnose'])
        def diagnose_command(message):
            self._diagnose_command(message)
    
    def _start_command(self, message):
        """Обработчик команды /start"""
        welcome_text = """
🤖 Добро пожаловать в бота мониторинга фриланс-заказов!

Я отслеживаю новые проекты на популярных фриланс-площадках и отправляю уведомления о подходящих заказах.

📋 Основные команды:
/help - Показать все команды
/status - Статус бота и настроек
/check - Запустить проверку вручную
/stats - Статистика за последние дни

⚙️ Настройки фильтров:
/keywords - Показать ключевые слова
/add_keyword <слово> - Добавить ключевое слово
/remove_keyword <слово> - Удалить ключевое слово
/budget <мин> [макс] - Установить бюджет

🔧 Управление платформами:
/platforms - Показать статус платформ
/toggle <платформа> - Включить/выключить платформу

📊 Информация:
/recent - Последние найденные проекты
/config - Текущая конфигурация

Бот готов к работе! 🚀
        """
        self.bot.reply_to(message, welcome_text)
        self.logger.info(f"Пользователь {message.from_user.id} запустил бота")
    
    def _help_command(self, message):
        """Обработчик команды /help"""
        help_text = """
📋 Все команды бота:

🔍 Мониторинг:
/status - Статус бота и настроек
/check - Запустить проверку вручную
/stats [дни] - Статистика (по умолчанию 7 дней)
/recent [часы] - Последние проекты (по умолчанию 24 часа)

⚙️ Ключевые слова:
/keywords - Показать все ключевые слова
/add_keyword <слово> - Добавить ключевое слово
/remove_keyword <слово> - Удалить ключевое слово

🚫 Исключения:
/excluded - Показать исключающие слова
/add_excluded <слово> - Добавить исключающее слово
/remove_excluded <слово> - Удалить исключающее слово

💰 Бюджет:
/budget - Показать текущие настройки бюджета
/budget <мин> - Установить минимальный бюджет
/budget <мин> <макс> - Установить диапазон бюджета
/set_min_budget <сумма> - Установить минимальный бюджет
/set_max_budget <сумма> - Установить максимальный бюджет
/remove_max_budget - Убрать ограничение по максимальному бюджету
/budget_info - Краткая информация о настройках бюджета

🌐 Платформы:
/platforms - Показать статус всех платформ
/toggle <платформа> - Включить/выключить платформу

🔧 Дополнительно:
/config - Показать полную конфигурацию
/diagnose - Диагностика платформ
/help - Показать эту справку
        """
        self.bot.reply_to(message, help_text)
    
    def _status_command(self, message):
        """Обработчик команды /status"""
        enabled_platforms = len(self.config.enabled_platforms)
        total_platforms = len(self.parser_manager.get_available_platforms())
        
        status_text = f"""
📊 Статус бота:

🔄 Мониторинг: Активен
⏱ Интервал проверки: {self.config.check_interval} секунд
🌐 Активных платформ: {enabled_platforms}/{total_platforms}

🔍 Фильтры:
📝 Ключевых слов: {len(self.config.keywords)}
🚫 Исключающих слов: {len(self.config.excluded_keywords)}
💰 Мин. бюджет: {self.config.min_budget} ₽
💰 Макс. бюджет: {self.config.max_budget or 'не установлен'} ₽

📈 Активные платформы:
{chr(10).join(f"• {platform}" for platform in self.config.enabled_platforms)}
        """
        self.bot.reply_to(message, status_text)
    
    def _check_command(self, message):
        """Обработчик команды /check"""
        self.bot.reply_to(message, "🔍 Запускаю проверку новых проектов...")
        
        # Запускаем проверку в отдельном потоке
        import threading
        thread = threading.Thread(target=self._run_check_async)
        thread.start()
        
        self.bot.send_message(message.chat.id, "✅ Проверка запущена!")
    
    def _run_check_async(self):
        """Запускает асинхронную проверку"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Здесь будет вызов основной функции проверки
            # projects = loop.run_until_complete(self.parser_manager.parse_all_platforms())
            # Пока что просто логируем
            self.logger.info("Ручная проверка запущена")
            
        except Exception as e:
            self.logger.error(f"Ошибка при ручной проверке: {e}")
    
    def _stats_command(self, message):
        """Обработчик команды /stats"""
        try:
            # Извлекаем количество дней из команды
            parts = message.text.split()
            days = 7  # по умолчанию
            if len(parts) > 1:
                try:
                    days = int(parts[1])
                    days = max(1, min(days, 90))  # ограничиваем от 1 до 90 дней
                except ValueError:
                    pass
            
            stats = self.database.get_statistics(days)
            
            stats_text = f"""
📊 Статистика за {days} дней:

📈 Общая статистика:
• Найдено проектов: {stats['general']['total_projects']}
• Отправлено уведомлений: {stats['general']['sent_notifications']}
• Средний бюджет: {stats['general']['avg_budget']:.0f} ₽
• Максимальный бюджет: {stats['general']['max_budget']} ₽

🌐 По платформам:
            """
            
            for platform_stat in stats['platforms']:
                stats_text += f"• {platform_stat['platform']}: {platform_stat['projects_count']} проектов\n"
            
            self.bot.reply_to(message, stats_text)
            
        except Exception as e:
            self.logger.error(f"Ошибка при получении статистики: {e}")
            self.bot.reply_to(message, "❌ Ошибка при получении статистики")
    
    def _add_keyword_command(self, message):
        """Обработчик команды /add_keyword"""
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            self.bot.reply_to(message, "❌ Укажите ключевое слово: /add_keyword python")
            return
        
        keyword = parts[1].strip()
        if self.config.add_keyword(keyword):
            self.bot.reply_to(message, f"✅ Ключевое слово '{keyword}' добавлено")
            self.logger.info(f"Добавлено ключевое слово: {keyword}")
        else:
            self.bot.reply_to(message, f"ℹ️ Ключевое слово '{keyword}' уже существует")
    
    def _remove_keyword_command(self, message):
        """Обработчик команды /remove_keyword"""
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            self.bot.reply_to(message, "❌ Укажите ключевое слово: /remove_keyword python")
            return
        
        keyword = parts[1].strip()
        if self.config.remove_keyword(keyword):
            self.bot.reply_to(message, f"✅ Ключевое слово '{keyword}' удалено")
            self.logger.info(f"Удалено ключевое слово: {keyword}")
        else:
            self.bot.reply_to(message, f"❌ Ключевое слово '{keyword}' не найдено")
    
    def _keywords_command(self, message):
        """Обработчик команды /keywords"""
        keywords = self.config.keywords
        if keywords:
            keywords_text = "📝 Ключевые слова:\n" + "\n".join(f"• {kw}" for kw in keywords)
        else:
            keywords_text = "📝 Ключевые слова не заданы"
        
        self.bot.reply_to(message, keywords_text)
    
    def _budget_command(self, message):
        """Обработчик команды /budget"""
        parts = message.text.split()
        
        if len(parts) == 1:
            # Показать текущие настройки
            max_budget_text = f"{self.config.max_budget} ₽" if self.config.max_budget else "не установлен"
            budget_text = f"""
💰 Настройки бюджета:
• Минимальный: {self.config.min_budget} ₽
• Максимальный: {max_budget_text}

📋 Команды для изменения:
/budget <мин> - установить минимальный
/budget <мин> <макс> - установить диапазон
/set_min_budget <сумма> - установить минимальный
/set_max_budget <сумма> - установить максимальный
/remove_max_budget - убрать максимальный

💡 Примеры:
/budget 5000 - мин. бюджет 5000 ₽
/budget 5000 50000 - от 5000 до 50000 ₽
/set_max_budget 100000 - макс. 100000 ₽
            """
            self.bot.reply_to(message, budget_text)
            return
        
        try:
            if len(parts) == 2:
                # Установить минимальный бюджет
                min_budget = int(parts[1])
                self.config.set('filters.min_budget', min_budget)
                self.bot.reply_to(message, f"✅ Минимальный бюджет установлен: {min_budget} ₽")
            elif len(parts) == 3:
                # Установить диапазон
                min_budget = int(parts[1])
                max_budget = int(parts[2])
                if max_budget < min_budget:
                    self.bot.reply_to(message, "❌ Максимальный бюджет не может быть меньше минимального")
                    return
                
                self.config.set('filters.min_budget', min_budget)
                self.config.set('filters.max_budget', max_budget)
                self.bot.reply_to(message, f"✅ Бюджет установлен: {min_budget} - {max_budget} ₽")
        except ValueError:
            self.bot.reply_to(message, "❌ Неверный формат. Используйте числа: /budget 5000 50000")

    def _set_min_budget_command(self, message):
        """Обработчик команды /set_min_budget"""
        parts = message.text.split()
        if len(parts) != 2:
            self.bot.reply_to(message, "❌ Укажите минимальный бюджет: /set_min_budget 5000")
            return

        try:
            min_budget = int(parts[1])
            max_budget = self.config.max_budget
            old_min = self.config.min_budget

            if max_budget and min_budget > max_budget:
                self.bot.reply_to(message, f"❌ Минимальный бюджет не может быть больше максимального ({max_budget} ₽)")
                return

            self.config.set('filters.min_budget', min_budget)
            self.bot.reply_to(message, f"✅ Минимальный бюджет установлен: {min_budget} ₽")
            self.logger.log_config_change('min_budget', old_min, min_budget)
        except ValueError:
            self.bot.reply_to(message, "❌ Неверный формат. Используйте число: /set_min_budget 5000")

    def _set_max_budget_command(self, message):
        """Обработчик команды /set_max_budget"""
        parts = message.text.split()
        if len(parts) != 2:
            self.bot.reply_to(message, "❌ Укажите максимальный бюджет: /set_max_budget 50000")
            return

        try:
            max_budget = int(parts[1])
            min_budget = self.config.min_budget

            if max_budget < min_budget:
                self.bot.reply_to(message, f"❌ Максимальный бюджет не может быть меньше минимального ({min_budget} ₽)")
                return

            old_max = self.config.max_budget
            self.config.set('filters.max_budget', max_budget)
            self.bot.reply_to(message, f"✅ Максимальный бюджет установлен: {max_budget} ₽")
            self.logger.log_config_change('max_budget', old_max, max_budget)
        except ValueError:
            self.bot.reply_to(message, "❌ Неверный формат. Используйте число: /set_max_budget 50000")

    def _remove_max_budget_command(self, message):
        """Обработчик команды /remove_max_budget"""
        old_max = self.config.max_budget
        if old_max is None:
            self.bot.reply_to(message, "ℹ️ Максимальный бюджет не установлен")
            return

        self.config.set('filters.max_budget', None)
        self.bot.reply_to(message, f"✅ Максимальный бюджет удален (был: {old_max} ₽)")
        self.logger.log_config_change('max_budget', old_max, None)

    def _budget_info_command(self, message):
        """Обработчик команды /budget_info - краткая информация о бюджете"""
        min_budget = self.config.min_budget
        max_budget = self.config.max_budget

        if max_budget:
            budget_range = f"{min_budget} - {max_budget} ₽"
            range_info = f"Диапазон: {budget_range}"
        else:
            budget_range = f"от {min_budget} ₽"
            range_info = f"Минимум: {min_budget} ₽, максимум не ограничен"

        info_text = f"""
💰 Бюджетные фильтры:
{range_info}

📊 Статус: {'Активен' if min_budget > 0 or max_budget else 'Не настроен'}
        """

        self.bot.reply_to(message, info_text)

    def _platforms_command(self, message):
        """Обработчик команды /platforms"""
        all_platforms = self.parser_manager.get_available_platforms()
        enabled = self.config.enabled_platforms
        
        platforms_text = "🌐 Статус платформ:\n\n"
        for platform in all_platforms:
            status = "✅" if platform in enabled else "❌"
            platforms_text += f"{status} {platform}\n"
        
        platforms_text += "\nДля переключения используйте: /toggle <платформа>"
        self.bot.reply_to(message, platforms_text)
    
    def _toggle_command(self, message):
        """Обработчик команды /toggle"""
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            self.bot.reply_to(message, "❌ Укажите платформу: /toggle fl_ru")
            return
        
        platform = parts[1].strip()
        available_platforms = self.parser_manager.get_available_platforms()
        
        if platform not in available_platforms:
            self.bot.reply_to(message, f"❌ Платформа '{platform}' не найдена")
            return
        
        is_enabled = self.config.toggle_platform(platform)
        status = "включена" if is_enabled else "выключена"
        self.bot.reply_to(message, f"✅ Платформа '{platform}' {status}")
        self.logger.log_platform_toggle(platform, is_enabled)
    
    def _recent_command(self, message):
        """Обработчик команды /recent"""
        try:
            parts = message.text.split()
            hours = 24  # по умолчанию
            if len(parts) > 1:
                try:
                    hours = int(parts[1])
                    hours = max(1, min(hours, 168))  # от 1 до 168 часов (неделя)
                except ValueError:
                    pass
            
            projects = self.database.get_recent_projects(hours, limit=10)
            
            if not projects:
                self.bot.reply_to(message, f"📭 Проектов за последние {hours} часов не найдено")
                return
            
            recent_text = f"📋 Последние проекты за {hours} часов:\n\n"
            for project in projects[:5]:  # Показываем только первые 5
                recent_text += f"💼 {project['title'][:50]}...\n"
                recent_text += f"💰 {project['budget']} ₽ | 🌐 {project['platform']}\n\n"
            
            if len(projects) > 5:
                recent_text += f"... и еще {len(projects) - 5} проектов"
            
            self.bot.reply_to(message, recent_text)
            
        except Exception as e:
            self.logger.error(f"Ошибка при получении последних проектов: {e}")
            self.bot.reply_to(message, "❌ Ошибка при получении данных")
    
    def _config_command(self, message):
        """Обработчик команды /config"""
        filter_summary = self.filter_manager.get_filter_summary()
        
        config_text = f"""
⚙️ Текущая конфигурация:

🔍 Фильтры:
• Ключевые слова: {len(filter_summary['keywords'])}
• Исключающие слова: {len(filter_summary['excluded_keywords'])}
• Мин. бюджет: {filter_summary['min_budget']} ₽
• Макс. бюджет: {filter_summary['max_budget'] or 'не установлен'} ₽
• Регулярные выражения: {'Да' if filter_summary['use_regex'] else 'Нет'}
• Учет регистра: {'Да' if filter_summary['case_sensitive'] else 'Нет'}

🌐 Платформы: {len(filter_summary['enabled_platforms'])}/{len(self.parser_manager.get_available_platforms())} активных

⏱ Интервал проверки: {self.config.check_interval} сек
        """
        
        self.bot.reply_to(message, config_text)
    
    def send_notification(self, project: Dict[str, Any]):
        """Отправляет уведомление о новом проекте"""
        try:
            message = f"💰 Новый заказ: {project['title']}\n"

            # Улучшенное отображение бюджета
            budget = project.get('budget', 0)
            budget_text = project.get('budget_text', '')

            if budget == -1:
                message += f"💵 Бюджет: по договоренности\n"
            elif budget > 0:
                # Если есть оригинальный текст с валютами, показываем его + рубли
                if budget_text and ('$' in budget_text or '€' in budget_text):
                    message += f"💵 Бюджет: {budget_text}\n"
                    message += f"💰 В рублях: ~{budget:,} ₽\n"
                else:
                    message += f"💵 Бюджет: {budget:,} ₽\n"
            elif budget_text:
                message += f"💵 Бюджет: {budget_text}\n"
            else:
                message += f"💵 Бюджет: не указан\n"

            message += f"🔍 Источник: {project['platform']}\n"

            if project.get('description'):
                desc = project['description'][:200]
                message += f"📝 Описание: {desc}...\n"

            if project.get('url'):
                message += f"🔗 Ссылка: {project['url']}"

            self.bot.send_message(self.config.telegram_chat_id, message)
            self.logger.log_project_sent(project)

        except Exception as e:
            self.logger.log_telegram_error(e)
    
    def start_polling(self):
        """Запускает polling бота"""
        try:
            self.logger.info("Запуск Telegram бота...")
            self.bot.polling(none_stop=True, interval=1)
        except Exception as e:
            self.logger.error(f"Ошибка в работе бота: {e}")
    
    # Дополнительные методы для обработки исключающих слов
    def _add_excluded_command(self, message):
        """Обработчик команды /add_excluded"""
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            self.bot.reply_to(message, "❌ Укажите исключающее слово: /add_excluded дизайн")
            return
        
        keyword = parts[1].strip()
        if self.config.add_excluded_keyword(keyword):
            self.bot.reply_to(message, f"✅ Исключающее слово '{keyword}' добавлено")
            self.logger.info(f"Добавлено исключающее слово: {keyword}")
        else:
            self.bot.reply_to(message, f"ℹ️ Исключающее слово '{keyword}' уже существует")
    
    def _remove_excluded_command(self, message):
        """Обработчик команды /remove_excluded"""
        parts = message.text.split(maxsplit=1)
        if len(parts) < 2:
            self.bot.reply_to(message, "❌ Укажите исключающее слово: /remove_excluded дизайн")
            return
        
        keyword = parts[1].strip()
        if self.config.remove_excluded_keyword(keyword):
            self.bot.reply_to(message, f"✅ Исключающее слово '{keyword}' удалено")
            self.logger.info(f"Удалено исключающее слово: {keyword}")
        else:
            self.bot.reply_to(message, f"❌ Исключающее слово '{keyword}' не найдено")
    
    def _excluded_command(self, message):
        """Обработчик команды /excluded"""
        excluded = self.config.excluded_keywords
        if excluded:
            excluded_text = "🚫 Исключающие слова:\n" + "\n".join(f"• {kw}" for kw in excluded)
        else:
            excluded_text = "🚫 Исключающие слова не заданы"
        
        self.bot.reply_to(message, excluded_text)

    def _diagnose_command(self, message):
        """Обработчик команды /diagnose"""
        self.bot.reply_to(message, "🔍 Запускаю диагностику платформ...")

        # Запускаем диагностику в отдельном потоке
        import threading
        thread = threading.Thread(target=self._run_diagnose_async, args=(message.chat.id,))
        thread.start()

    def _run_diagnose_async(self, chat_id):
        """Запускает асинхронную диагностику"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Запускаем диагностику
            results = loop.run_until_complete(self.parser_manager.diagnose_platforms())

            # Формируем отчет
            report = "🔍 Диагностика платформ:\n\n"

            working_count = 0
            total_count = len(results)

            for platform, result in results.items():
                if result.get('available', False):
                    status = "✅ Работает"
                    working_count += 1
                    if result.get('has_projects', False):
                        status += f" ({result.get('content_length', 0)} байт)"
                    else:
                        status += " (проекты не найдены)"
                else:
                    status = "❌ Недоступна"
                    if result.get('error'):
                        status += f" - {result['error'][:50]}..."

                report += f"• {platform}: {status}\n"

            report += f"\n📊 Итого: {working_count}/{total_count} платформ работают"

            self.bot.send_message(chat_id, report)

        except Exception as e:
            self.logger.error(f"Ошибка при диагностике: {e}")
            self.bot.send_message(chat_id, f"❌ Ошибка при диагностике: {str(e)}")