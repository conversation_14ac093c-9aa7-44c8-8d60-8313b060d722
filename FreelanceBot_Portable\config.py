import os
import json
from typing import List, Dict, Any, Optional

class Config:
    """Класс для управления конфигурацией бота"""
    
    def __init__(self, config_file: str = 'config.json'):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Загружает конфигурацию из файла или создает дефолтную"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Ошибка при загрузке конфигурации: {e}")
                return self._default_config()
        else:
            config = self._default_config()
            # Сохраняем дефолтную конфигурацию
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"Ошибка при создании конфигурации: {e}")
            return config
    
    def _default_config(self) -> Dict[str, Any]:
        """Возвращает дефолтную конфигурацию"""
        return {
            "telegram": {
                "token": "ВАШ_ТОКЕН_БОТА",
                "chat_id": "ВАШ_ЧАТ_ID",
                "admin_ids": []
            },
            "monitoring": {
                "check_interval": 60,  # секунды
                "enabled_platforms": [
                    "fl_ru",
                    "freelance_ru", 
                    "habr_freelance",
                    "kwork",
                    "youdo",
                    "weblancer",
                    "upwork",
                    "freelansim"
                ]
            },
            "filters": {
                "keywords": [
                    "python",
                    "telegram",
                    "бот",
                    "парсинг",
                    "django",
                    "flask",
                    "fastapi",
                    "веб-разработка",
                    "backend",
                    "api"
                ],
                "excluded_keywords": [
                    "дизайн",
                    "фотошоп",
                    "логотип",
                    "баннер"
                ],
                "min_budget": 3000,
                "max_budget": None,
                "use_regex": False,
                "case_sensitive": False
            },
            "database": {
                "file": "projects.db"
            },
            "logging": {
                "level": "INFO",
                "file": "bot.log",
                "max_size_mb": 10,
                "backup_count": 5
            },
            "notifications": {
                "include_description": True,
                "max_description_length": 200,
                "send_summary": True,
                "summary_interval": 3600  # секунды
            }
        }
    
    def save_config(self):
        """Сохраняет текущую конфигурацию в файл"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Ошибка при сохранении конфигурации: {e}")
    
    def get(self, key: str, default=None):
        """Получает значение из конфигурации по ключу"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value):
        """Устанавливает значение в конфигурации"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()
    
    def add_keyword(self, keyword: str):
        """Добавляет ключевое слово"""
        keywords = self.get('filters.keywords', [])
        if keyword not in keywords:
            keywords.append(keyword)
            self.set('filters.keywords', keywords)
            return True
        return False
    
    def remove_keyword(self, keyword: str):
        """Удаляет ключевое слово"""
        keywords = self.get('filters.keywords', [])
        if keyword in keywords:
            keywords.remove(keyword)
            self.set('filters.keywords', keywords)
            return True
        return False
    
    def add_excluded_keyword(self, keyword: str):
        """Добавляет исключающее ключевое слово"""
        excluded = self.get('filters.excluded_keywords', [])
        if keyword not in excluded:
            excluded.append(keyword)
            self.set('filters.excluded_keywords', excluded)
            return True
        return False
    
    def remove_excluded_keyword(self, keyword: str):
        """Удаляет исключающее ключевое слово"""
        excluded = self.get('filters.excluded_keywords', [])
        if keyword in excluded:
            excluded.remove(keyword)
            self.set('filters.excluded_keywords', excluded)
            return True
        return False
    
    def toggle_platform(self, platform: str):
        """Включает/выключает платформу"""
        enabled = self.get('monitoring.enabled_platforms', [])
        if platform in enabled:
            enabled.remove(platform)
        else:
            enabled.append(platform)
        self.set('monitoring.enabled_platforms', enabled)
        return platform in enabled
    
    # Свойства для быстрого доступа
    @property
    def telegram_token(self) -> str:
        return self.get('telegram.token')
    
    @property
    def telegram_chat_id(self) -> str:
        return self.get('telegram.chat_id')
    
    @property
    def admin_ids(self) -> List[str]:
        return self.get('telegram.admin_ids', [])
    
    @property
    def check_interval(self) -> int:
        return self.get('monitoring.check_interval', 60)
    
    @property
    def enabled_platforms(self) -> List[str]:
        return self.get('monitoring.enabled_platforms', [])
    
    @property
    def keywords(self) -> List[str]:
        return self.get('filters.keywords', [])
    
    @property
    def excluded_keywords(self) -> List[str]:
        return self.get('filters.excluded_keywords', [])
    
    @property
    def min_budget(self) -> int:
        return self.get('filters.min_budget', 0)
    
    @property
    def max_budget(self) -> Optional[int]:
        return self.get('filters.max_budget')
    
    @property
    def database_file(self) -> str:
        return self.get('database.file', 'projects.db')
    
    @property
    def log_file(self) -> str:
        return self.get('logging.file', 'bot.log')
    
    @property
    def log_level(self) -> str:
        return self.get('logging.level', 'INFO')
