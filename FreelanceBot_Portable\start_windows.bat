@echo off
chcp 65001 > nul
title Freelance Bot v1.0

echo.
echo ========================================
echo    🚀 Freelance Bot v1.0
echo ========================================
echo.

REM Проверяем Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python не установлен!
    echo    Скачайте с https://python.org
    echo    Минимальная версия: Python 3.8
    pause
    exit /b 1
)

echo ✅ Python найден
echo.

REM Проверяем зависимости
echo 📦 Проверка зависимостей...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Устанавливаем зависимости...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Ошибка установки зависимостей!
        pause
        exit /b 1
    )
) else (
    echo ✅ Зависимости установлены
)

echo.

REM Проверяем конфигурацию
if not exist config.json (
    echo ❌ Файл config.json не найден!
    echo    Скопируйте config.example.json в config.json
    echo    И настройте telegram_token и telegram_chat_id
    pause
    exit /b 1
)

echo ✅ Конфигурация найдена
echo.

REM Запускаем бота
echo 🔄 Запуск бота...
echo    Для остановки нажмите Ctrl+C
echo.
python main.py

echo.
echo ⚠️ Бот остановлен
pause
