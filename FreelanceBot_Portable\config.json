{"telegram_token": "YOUR_BOT_TOKEN_HERE", "telegram_chat_id": "YOUR_CHAT_ID_HERE", "database_file": "projects.db", "check_interval": 60, "enabled_platforms": ["fl_ru", "freelance_ru", "habr_freelance", "kwork", "youdo", "weblancer", "upwork", "freelansim"], "keywords": ["python", "django", "flask", "<PERSON><PERSON><PERSON>", "javascript", "react", "vue", "angular", "node.js", "php", "laravel", "wordpress", "java", "spring", "c#", ".net", "asp.net", "go", "golang", "rust", "mobile", "android", "ios", "react native", "flutter", "web", "frontend", "backend", "fullstack", "api", "rest", "graphql", "database", "sql", "postgresql", "mysql", "mongodb", "devops", "docker", "kubernetes", "machine learning", "ai", "data science", "blockchain", "crypto", "game", "unity", "unreal", "design", "ui", "ux", "figma", "seo", "marketing", "telegram bot", "chatbot", "парс<PERSON><PERSON><PERSON>", "scraping", "crawler", "автоматизация", "automation", "интеграция", "integration", "тестирование", "testing", "верстка", "layout", "html", "css", "администрирование", "admin", "консультация", "consultation", "обучение", "training", "курс", "техническое задание", "тз", "доработка", "improvement", "исправление", "fix", "bug", "оптимизация", "optimization", "миграция", "migration", "настройка", "setup", "configuration", "поддержка", "support", "maintenance", "разработка", "development", "dev", "программирование", "programming", "сайт", "website", "site", "приложение", "application", "app", "система", "system", "платформа", "platform", "сервис", "service", "модуль", "module", "компонент", "component", "библиотека", "library", "фреймворк", "framework", "архитектура", "architecture", "проектирование", "design", "аналитика", "analytics", "отчеты", "reports", "dashboard", "да<PERSON>борд", "crm", "erp", "e-commerce", "интернет-магазин", "landing", "лендинг", "корпоративный", "corporate", "стартап", "startup", "mvp", "прототип", "prototype", "концепция", "concept", "техническая документация", "code review", "ревью кода", "рефакторинг", "refactoring", "legacy", "наследие", "микросервисы", "microservices", "монолит", "monolith", "cloud", "облако", "aws", "azure", "gcp", "serverless", "ci/cd", "pipeline", "git", "github", "gitlab", "agile", "scrum", "kanban", "удаленно", "remote", "фрил<PERSON><PERSON><PERSON>", "freelance", "проект", "project", "задача", "task", "зак<PERSON>з", "order", "работа", "work", "job"], "exclude_keywords": ["только для женщин", "только девушки", "женский", "модель", "фото", "видео", "контент для взрослых", "18+", "эротика", "порно", "казино", "ставки", "азартные игры", "форекс", "бинарные опционы", "пирамида", "мошенничество", "обман", "развод", "лохотрон", "спам", "рассылка", "накрутка", "лайки", "подписчики", "боты", "фейк", "fake", "только Москва", "только СПб", "только офис", "переезд", "релокация", "студент", "ста<PERSON><PERSON>р", "без опыта", "бесплатно", "за идею", "за процент", "партнерство", "инвестиции", "вложения"], "min_budget": 0, "max_budget": null, "log_level": "INFO", "max_projects_per_notification": 5, "notification_delay": 1}