{"telegram": {"token": "**********************************************", "chat_id": "2081516899", "admin_ids": [2081516899]}, "monitoring": {"check_interval": 60, "enabled_platforms": ["fl_ru", "freelance_ru", "habr_freelance", "kwork", "youdo", "weblancer", "upwork", "freelansim"]}, "filters": {"keywords": ["python", "программирование", "разработка", "скрипт", "автоматизация", "парс<PERSON><PERSON><PERSON>", "бот", "telegram", "django", "flask", "<PERSON><PERSON><PERSON>", "веб-разработка", "backend", "api", "простая задача", "легкая работа", "быстрая задача", "небольшая работа", "срочно", "простой проект", "мелкая работа", "копирование", "ввод данных", "обработка данных", "excel", "csv", "json", "база данных", "sql", "javascript", "js", "nodejs", "react", "vue", "angular", "php", "wordpress", "laravel", "mysql", "postgresql", "mongodb", "redis", "docker", "git", "github", "bitbucket", "linux", "ubuntu", "centos", "nginx", "apache", "rest", "graphql", "microservices", "aws", "azure", "google cloud", "<PERSON><PERSON>", "digitalocean", "vps", "сервер", "хостинг", "домен", "ssl", "https", "безопасность", "pytest", "selenium", "ci/cd", "devops", "ansible", "terraform", "kubernetes", "мобильное приложение", "android", "ios", "flutter", "react native", "kotlin", "swift", "java", "c#", "c++", "go", "rust", "ruby", "rails", "машинное обучение", "ml", "ai", "искусственный интеллект", "нейронные сети", "tensorflow", "pytorch", "opencv", "pandas", "numpy", "scipy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jup<PERSON><PERSON>", "data science", "анализ данных", "big data", "elasticsearch", "kibana", "grafana", "prometheus", "blockchain", "криптовалюта", "smart contract", "solidity", "web3", "defi", "nft"], "excluded_keywords": ["ди<PERSON><PERSON><PERSON>н", "фотошоп", "логотип", "бан<PERSON><PERSON><PERSON>", "верстка", "frontend", "react", "vue", "angular", "figma", "photoshop", "illustrator", "видеомонтаж", "видео", "анимация", "3d", "моделирование", "мар<PERSON><PERSON><PERSON><PERSON><PERSON>г", "реклама", "smm", "контент", "копир<PERSON><PERSON><PERSON>инг", "переводы", "тексты"], "min_budget": 0, "max_budget": null, "use_regex": false, "case_sensitive": false}, "database": {"file": "projects.db"}, "logging": {"level": "INFO", "file": "bot.log", "max_size_mb": 10, "backup_count": 5}, "notifications": {"include_description": true, "max_description_length": 200, "send_summary": true, "summary_interval": 3600}}