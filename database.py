import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from contextlib import contextmanager

class Database:
    """Класс для работы с базой данных проектов"""
    
    def __init__(self, db_file: str = 'projects.db'):
        self.db_file = db_file
        self.init_database()
    
    def init_database(self):
        """Инициализирует базу данных и создает таблицы"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Таблица проектов
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    platform TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    budget INTEGER,
                    currency TEXT DEFAULT 'RUB',
                    url TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    found_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sent_notification BOOLEAN DEFAULT FALSE,
                    sent_at TIMESTAMP,
                    raw_data TEXT,
                    hash TEXT UNIQUE
                )
            ''')
            
            # Таблица статистики
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS statistics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    platform TEXT NOT NULL,
                    projects_found INTEGER DEFAULT 0,
                    notifications_sent INTEGER DEFAULT 0,
                    UNIQUE(date, platform)
                )
            ''')
            
            # Таблица настроек
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Индексы для оптимизации
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_projects_platform ON projects(platform)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_projects_found_at ON projects(found_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_projects_sent ON projects(sent_notification)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(date)')
            
            conn.commit()
    
    @contextmanager
    def get_connection(self):
        """Контекстный менеджер для работы с соединением"""
        conn = sqlite3.connect(self.db_file)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def generate_project_hash(self, project: Dict[str, Any]) -> str:
        """Генерирует хеш проекта для проверки дубликатов"""
        # Используем title, platform и url для создания уникального хеша
        hash_string = f"{project.get('title', '')}{project.get('platform', '')}{project.get('url', '')}"
        return hashlib.md5(hash_string.encode('utf-8')).hexdigest()
    
    def add_project(self, project: Dict[str, Any]) -> bool:
        """
        Добавляет проект в базу данных
        
        Args:
            project: Словарь с данными проекта
            
        Returns:
            True если проект добавлен, False если уже существует
        """
        project_hash = self.generate_project_hash(project)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Проверяем, существует ли уже такой проект
            cursor.execute('SELECT id FROM projects WHERE hash = ?', (project_hash,))
            if cursor.fetchone():
                return False
            
            # Добавляем новый проект
            # Создаем уникальный ID с префиксом платформы
            platform = project.get('platform', '')
            original_id = project.get('id', project_hash)
            project_id = f"{platform}_{original_id}"

            cursor.execute('''
                INSERT INTO projects (
                    id, platform, title, description, budget, currency,
                    url, raw_data, hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                project_id,
                platform,
                project.get('title', ''),
                project.get('description', ''),
                project.get('budget', 0),
                project.get('currency', 'RUB'),
                project.get('url', ''),
                json.dumps(project, ensure_ascii=False),
                project_hash
            ))
            
            conn.commit()
            return True
    
    def mark_as_sent(self, project_id: str) -> bool:
        """Отмечает проект как отправленный"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE projects
                SET sent_notification = TRUE, sent_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (project_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_unsent_projects(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Получает неотправленные проекты"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM projects 
                WHERE sent_notification = FALSE 
                ORDER BY found_at DESC 
                LIMIT ?
            ''', (limit,))
            
            projects = []
            for row in cursor.fetchall():
                project = dict(row)
                if project['raw_data']:
                    try:
                        raw_data = json.loads(project['raw_data'])
                        project.update(raw_data)
                    except json.JSONDecodeError:
                        pass
                projects.append(project)
            
            return projects
    
    def get_projects_by_platform(self, platform: str, days: int = 7) -> List[Dict[str, Any]]:
        """Получает проекты по платформе за указанное количество дней"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            since_date = datetime.now() - timedelta(days=days)
            cursor.execute('''
                SELECT * FROM projects 
                WHERE platform = ? AND found_at >= ?
                ORDER BY found_at DESC
            ''', (platform, since_date))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Получает статистику за указанное количество дней"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            since_date = datetime.now() - timedelta(days=days)
            
            # Общая статистика
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_projects,
                    COUNT(CASE WHEN sent_notification = TRUE THEN 1 END) as sent_notifications,
                    AVG(budget) as avg_budget,
                    MAX(budget) as max_budget,
                    MIN(budget) as min_budget
                FROM projects 
                WHERE found_at >= ?
            ''', (since_date,))
            
            general_stats = dict(cursor.fetchone())
            
            # Статистика по платформам
            cursor.execute('''
                SELECT 
                    platform,
                    COUNT(*) as projects_count,
                    COUNT(CASE WHEN sent_notification = TRUE THEN 1 END) as notifications_sent,
                    AVG(budget) as avg_budget
                FROM projects 
                WHERE found_at >= ?
                GROUP BY platform
                ORDER BY projects_count DESC
            ''', (since_date,))
            
            platform_stats = [dict(row) for row in cursor.fetchall()]
            
            return {
                'general': general_stats,
                'platforms': platform_stats,
                'period_days': days
            }
    
    def update_daily_statistics(self, platform: str, projects_found: int, notifications_sent: int):
        """Обновляет ежедневную статистику"""
        today = datetime.now().date()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO statistics (date, platform, projects_found, notifications_sent)
                VALUES (?, ?, 
                    COALESCE((SELECT projects_found FROM statistics WHERE date = ? AND platform = ?), 0) + ?,
                    COALESCE((SELECT notifications_sent FROM statistics WHERE date = ? AND platform = ?), 0) + ?
                )
            ''', (today, platform, today, platform, projects_found, today, platform, notifications_sent))
            conn.commit()
    
    def cleanup_old_projects(self, days: int = 30):
        """Удаляет старые проекты"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM projects WHERE found_at < ?', (cutoff_date,))
            deleted_count = cursor.rowcount
            conn.commit()
            
            return deleted_count
    
    def get_setting(self, key: str, default=None):
        """Получает настройку из базы данных"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT value FROM settings WHERE key = ?', (key,))
            row = cursor.fetchone()
            
            if row:
                try:
                    return json.loads(row['value'])
                except json.JSONDecodeError:
                    return row['value']
            
            return default
    
    def set_setting(self, key: str, value):
        """Сохраняет настройку в базу данных"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False)
            else:
                value_str = str(value)
            
            cursor.execute('''
                INSERT OR REPLACE INTO settings (key, value, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value_str))
            conn.commit()
    
    def get_recent_projects(self, hours: int = 24, limit: int = 50) -> List[Dict[str, Any]]:
        """Получает недавние проекты"""
        since_time = datetime.now() - timedelta(hours=hours)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM projects 
                WHERE found_at >= ?
                ORDER BY found_at DESC
                LIMIT ?
            ''', (since_time, limit))
            
            return [dict(row) for row in cursor.fetchall()]
