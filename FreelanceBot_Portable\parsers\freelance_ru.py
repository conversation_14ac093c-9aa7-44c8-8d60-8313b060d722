from typing import List, Dict, Any
from bs4 import BeautifulSoup
from . import BaseParser

class FreelanceRuParser(BaseParser):
    """Парсер для Freelance.ru"""
    
    def __init__(self, logger=None):
        super().__init__(
            name="Freelance.ru",
            base_url="https://freelance.ru",
            logger=logger
        )
    
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с Freelance.ru"""
        projects = []
        
        try:
            # Основная страница с проектами
            url = f"{self.base_url}/project/search"
            content = await self._make_request(url)
            
            if not content:
                self.log_warning("Не удалось получить содержимое страницы")
                return projects
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Ищем карточки проектов (обновленные селекторы для Freelance.ru)
            project_cards = soup.find_all('a', href=lambda x: x and '/projects/' in x)
            if not project_cards:
                project_cards = soup.find_all('div', class_='project-item')
            if not project_cards:
                project_cards = soup.find_all('article', class_='project')
            if not project_cards:
                project_cards = soup.find_all('div', class_='task-item')
            
            self.log_info(f"Найдено {len(project_cards)} карточек проектов")
            
            for card in project_cards:
                try:
                    project = await self._parse_project_card(card)
                    if project:
                        projects.append(project)
                except Exception as e:
                    self.log_error(f"Ошибка при обработке карточки проекта: {e}")
            
            self.log_info(f"Успешно обработано {len(projects)} проектов")
            
        except Exception as e:
            self.log_error(f"Ошибка при парсинге Freelance.ru: {e}", exc_info=True)
        
        return projects
    
    async def _parse_project_card(self, card) -> Dict[str, Any]:
        """Парсит отдельную карточку проекта"""
        project = {
            'platform': 'Freelance.ru',
            'source': 'Freelance.ru'
        }

        # Для ссылок на проекты извлекаем информацию из href
        if card.name == 'a' and card.get('href'):
            href = card.get('href')
            if href.startswith('/'):
                project['url'] = f"{self.base_url}{href}"
            else:
                project['url'] = href
            project['id'] = self._extract_project_id(project['url'])
            project['title'] = self._clean_text(card.get_text())
        else:
            # Извлекаем заголовок и ссылку из обычных карточек
            title_elem = card.find('h3', class_='project-title')
            if not title_elem:
                title_elem = card.find('h2', class_='task-title')
            if not title_elem:
                title_elem = card.find('a', class_='project-link')

            if title_elem:
                link_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem

                if link_elem:
                    project['title'] = self._clean_text(link_elem.get_text())
                    href = link_elem.get('href')
                    if href:
                        if href.startswith('/'):
                            project['url'] = f"{self.base_url}{href}"
                        else:
                            project['url'] = href
                        project['id'] = self._extract_project_id(project['url'])
                else:
                    project['title'] = self._clean_text(title_elem.get_text())

        if not project.get('title'):
            return None
        
        # Извлекаем бюджет
        budget_elem = card.find('div', class_='project-budget')
        if not budget_elem:
            budget_elem = card.find('span', class_='budget')
        if not budget_elem:
            budget_elem = card.find('div', class_='price')
        
        if budget_elem:
            budget_text = self._clean_text(budget_elem.get_text())
            project['budget'] = self._parse_budget(budget_text)
            project['budget_text'] = budget_text
        else:
            project['budget'] = 0
            project['budget_text'] = ""
        
        # Извлекаем описание
        desc_elem = card.find('div', class_='project-description')
        if not desc_elem:
            desc_elem = card.find('div', class_='task-description')
        if not desc_elem:
            desc_elem = card.find('p', class_='description')
        
        if desc_elem:
            project['description'] = self._clean_text(desc_elem.get_text())
        else:
            project['description'] = ""
        
        # Извлекаем дату
        date_elem = card.find('time')
        if not date_elem:
            date_elem = card.find('span', class_='date')
        if not date_elem:
            date_elem = card.find('div', class_='project-date')
        
        if date_elem:
            project['published_date'] = self._clean_text(date_elem.get_text())
        
        # Извлекаем категорию
        category_elem = card.find('a', class_='category')
        if not category_elem:
            category_elem = card.find('span', class_='project-category')
        
        if category_elem:
            project['category'] = self._clean_text(category_elem.get_text())
        
        # Извлекаем информацию о заказчике
        customer_elem = card.find('a', class_='customer')
        if not customer_elem:
            customer_elem = card.find('div', class_='project-customer')
        
        if customer_elem:
            project['customer'] = self._clean_text(customer_elem.get_text())
        
        # Извлекаем количество откликов
        responses_elem = card.find('span', class_='responses')
        if not responses_elem:
            responses_elem = card.find('div', class_='project-responses')
        
        if responses_elem:
            responses_text = self._clean_text(responses_elem.get_text())
            project['responses_count'] = self._extract_number(responses_text)
        
        # Извлекаем навыки/теги
        skills_container = card.find('div', class_='project-skills')
        if not skills_container:
            skills_container = card.find('div', class_='tags')
        
        if skills_container:
            skills = []
            skill_elements = skills_container.find_all(['a', 'span'])
            for skill_elem in skill_elements:
                skill_text = self._clean_text(skill_elem.get_text())
                if skill_text and skill_text not in skills:
                    skills.append(skill_text)
            project['skills'] = skills
        
        # Извлекаем тип проекта
        type_elem = card.find('span', class_='project-type')
        if type_elem:
            project['project_type'] = self._clean_text(type_elem.get_text())
        
        # Проверяем обязательные поля
        if not project.get('title'):
            return None
        
        # Если нет ID, генерируем его из заголовка
        if not project.get('id'):
            import hashlib
            project['id'] = hashlib.md5(project['title'].encode()).hexdigest()[:8]
        
        return project
    
    def _extract_number(self, text: str) -> int:
        """Извлекает число из текста"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _parse_budget(self, budget_text: str) -> int:
        """Парсит бюджет специфично для Freelance.ru"""
        if not budget_text:
            return 0
        
        budget_text = budget_text.lower().replace(' ', '')
        
        # Удаляем валютные символы
        budget_text = budget_text.replace('₽', '').replace('руб', '').replace('рублей', '')
        budget_text = budget_text.replace('$', '').replace('usd', '').replace('долларов', '')
        budget_text = budget_text.replace('€', '').replace('eur', '').replace('евро', '')
        
        # Обрабатываем специфичные для Freelance.ru форматы
        if 'договорная' in budget_text or 'договор' in budget_text:
            return 0
        
        if 'почасовая' in budget_text:
            # Пытаемся извлечь почасовую ставку
            import re
            numbers = re.findall(r'\d+', budget_text)
            if numbers:
                hourly_rate = int(numbers[0])
                # Предполагаем 40 часов работы для оценки общего бюджета
                return hourly_rate * 40
        
        # Обрабатываем диапазоны
        if '-' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text)
            if len(numbers) >= 2:
                # Берем минимальное значение из диапазона
                return int(numbers[0])
        
        # Обычное извлечение числа
        return super()._parse_budget(budget_text)
