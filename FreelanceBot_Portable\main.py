
import asyncio
import logging
from datetime import datetime
import sys
import os

# Добавляем текущую директорию в путь для импорта модулей
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from database import Database
from filters import FilterManager
from bot_handlers import BotHandlers
from logger import setup_logger, BotLogger
from parsers import ParserManager
import time

class FreelanceBot:
    """Основной класс бота для мониторинга фриланс-заказов"""

    def __init__(self):
        self.config = Config()
        self.logger = BotLogger(self.config)
        self.database = Database(self.config.database_file)
        self.filter_manager = FilterManager(self.config, self.logger)
        self.parser_manager = ParserManager(self.config, self.logger)
        self.bot_handlers = None  # Создадим позже после проверки конфигурации
        self.running = False

    async def check_new_projects(self):
        """Проверяет новые проекты на всех платформах"""
        try:
            self.logger.log_monitoring_cycle_start()

            # Получаем проекты со всех платформ
            all_projects = await self.parser_manager.parse_all_platforms()

            new_projects_count = 0
            notifications_sent = 0

            for project in all_projects:
                # Добавляем проект в базу данных
                if self.database.add_project(project):
                    new_projects_count += 1
                    self.logger.log_project_found(project, project['platform'])

                    # Проверяем фильтры
                    should_notify, reason = self.filter_manager.should_notify(project)

                    if should_notify:
                        # Отправляем уведомление
                        if self.bot_handlers:
                            self.bot_handlers.send_notification(project)

                        # Отмечаем как отправленное (используем ID с префиксом платформы)
                        platform = project.get('platform', '')
                        original_id = project.get('id')

                        # Если ID нет, используем хеш проекта
                        if not original_id:
                            project_hash = self.database.generate_project_hash(project)
                            original_id = project_hash

                        project_id = f"{platform}_{original_id}"
                        self.database.mark_as_sent(project_id)
                        notifications_sent += 1

                        # Обновляем статистику
                        self.database.update_daily_statistics(
                            project['platform'], 1, 1
                        )

            self.logger.log_monitoring_cycle_end(new_projects_count, notifications_sent)

        except Exception as e:
            self.logger.error(f"Ошибка при проверке проектов: {e}", exc_info=True)

    def run_monitoring_cycle(self):
        """Запускает один цикл мониторинга"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.check_new_projects())
            loop.close()
        except Exception as e:
            self.logger.error(f"Ошибка в цикле мониторинга: {e}", exc_info=True)

    def start_monitoring(self):
        """Запускает мониторинг в отдельном потоке"""
        import threading
        import schedule

        # Настраиваем расписание
        schedule.every(self.config.check_interval).seconds.do(self.run_monitoring_cycle)

        def monitoring_worker():
            self.logger.info("Запуск мониторинга проектов...")
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(1)
                except Exception as e:
                    self.logger.error(f"Ошибка в мониторинге: {e}")
                    time.sleep(5)

        self.running = True
        monitoring_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitoring_thread.start()

        # Запускаем первую проверку через 30 секунд
        threading.Timer(30.0, self.run_monitoring_cycle).start()

    def start(self):
        """Запускает бота"""
        try:
            self.logger.log_bot_start()

            # Проверяем конфигурацию
            if not self.config.telegram_token or self.config.telegram_token == "ВАШ_ТОКЕН_БОТА":
                self.logger.error("Не настроен токен Telegram бота!")
                print("❌ Ошибка: Не настроен токен Telegram бота!")
                print("📋 Инструкция по настройке:")
                print("1. Найдите @BotFather в Telegram")
                print("2. Создайте нового бота командой /newbot")
                print("3. Скопируйте токен (формат: 123456789:ABCdef...)")
                print("4. Вставьте токен в config.json в поле 'token'")
                print("5. Подробная инструкция в файле SETUP_INSTRUCTIONS.md")
                return

            if not self.config.telegram_chat_id or self.config.telegram_chat_id == "ВАШ_ЧАТ_ID":
                self.logger.error("Не настроен Chat ID!")
                print("❌ Ошибка: Не настроен Chat ID!")
                print("📋 Инструкция по получению Chat ID:")
                print("1. Найдите @userinfobot в Telegram")
                print("2. Отправьте ему любое сообщение")
                print("3. Скопируйте ваш ID (например: 123456789)")
                print("4. Вставьте ID в config.json в поле 'chat_id'")
                print("5. Подробная инструкция в файле SETUP_INSTRUCTIONS.md")
                return

            # Проверяем формат токена
            if ':' not in self.config.telegram_token:
                self.logger.error("Неправильный формат токена!")
                print("❌ Ошибка: Неправильный формат токена!")
                print("🔍 Токен должен содержать двоеточие (:)")
                print("📝 Правильный формат: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz")
                print("🤖 Получите правильный токен у @BotFather")
                return

            # Создаем BotHandlers после проверки конфигурации
            self.bot_handlers = BotHandlers(
                self.config,
                self.database,
                self.filter_manager,
                self.parser_manager,
                self.logger
            )

            # Запускаем мониторинг
            self.start_monitoring()

            # Выводим информацию о запуске
            print("🤖 Бот запущен!")
            print(f"⏱ Интервал проверки: {self.config.check_interval} секунд")
            print(f"🌐 Активных платформ: {len(self.config.enabled_platforms)}")
            print(f"🔰 Ключевых слов: {len(self.config.keywords)}")
            print(f"💰 Минимальный бюджет: {self.config.min_budget} ₽")
            max_budget_text = f"{self.config.max_budget} ₽" if self.config.max_budget else "не ограничен"
            print(f"💎 Максимальный бюджет: {max_budget_text}")
            print("📱 Запуск Telegram бота...")

            # Запускаем Telegram бота (блокирующий вызов)
            self.bot_handlers.start_polling()

        except KeyboardInterrupt:
            self.stop()
        except Exception as e:
            self.logger.error(f"Критическая ошибка: {e}", exc_info=True)
            print(f"❌ Критическая ошибка: {e}")

    def stop(self):
        """Останавливает бота"""
        self.running = False
        self.logger.log_bot_stop()
        print("🛑 Бот остановлен")

if __name__ == '__main__':
    # Создаем и запускаем бота
    bot = FreelanceBot()
    bot.start()
