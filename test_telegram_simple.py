#!/usr/bin/env python3
"""
Простой тест подключения к Telegram
"""

import os
import sys

# Добавляем текущую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_telegram():
    """Тестирует подключение к Telegram"""
    print("📱 Тестирование подключения к Telegram...")
    
    try:
        import telebot
        import requests
        from config import Config
        
        config = Config()
        
        # Полностью отключаем прокси для requests
        session = requests.Session()
        session.proxies = {}
        session.trust_env = False
        
        # Временно отключаем прокси через переменные окружения
        old_proxies = {}
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
        
        for var in proxy_vars:
            if var in os.environ:
                old_proxies[var] = os.environ[var]
                del os.environ[var]
        
        try:
            # Патчим telebot для использования нашей сессии
            import telebot.apihelper
            telebot.apihelper.SESSION = session
            
            # Создаем бота
            bot = telebot.TeleBot(config.telegram_token)
            
            # Пытаемся получить информацию о боте
            bot_info = bot.get_me()
            print(f"✅ Подключение к Telegram успешно!")
            print(f"  - Имя бота: {bot_info.first_name}")
            print(f"  - Username: @{bot_info.username}")
            
            return True
            
        finally:
            # Восстанавливаем прокси если они были
            for var, value in old_proxies.items():
                os.environ[var] = value
        
    except Exception as e:
        print(f"❌ Ошибка подключения к Telegram: {e}")
        return False

if __name__ == "__main__":
    success = test_telegram()
    if success:
        print("\n🎉 Telegram работает! Можно запускать бота.")
    else:
        print("\n⚠️ Есть проблемы с Telegram.")
