{"telegram": {"token": "**********************************************", "chat_id": "2081516899", "admin_ids": [2081516899]}, "monitoring": {"check_interval": 60, "enabled_platforms": ["fl_ru", "freelance_ru", "habr_freelance", "kwork", "youdo", "weblancer", "upwork", "freelansim"]}, "filters": {"keywords": ["python разработчик", "python программист", "python скрипт", "python автоматизация", "python парсинг", "python бот", "telegram бот", "django разработка", "flask приложение", "fastapi разработка", "python backend", "python api", "веб-скрапинг", "пар<PERSON><PERSON>р са<PERSON>тов", "автоматизация процессов", "обработка данных python", "python для бизнеса", "интеграция api", "python консультация", "доработка python", "исправление python", "python оптимизация", "веб-разработка", "backend", "api", "простая задача", "легкая работа", "быстрая задача", "небольшая работа", "срочно", "простой проект", "мелкая работа", "копирование", "ввод данных", "обработка данных", "excel", "csv", "json", "база данных", "sql", "javascript", "js", "nodejs", "react", "vue", "angular", "php", "wordpress", "laravel", "mysql", "postgresql", "mongodb", "redis", "docker", "git", "github", "bitbucket", "linux", "ubuntu", "centos", "nginx", "apache", "rest", "graphql", "microservices", "aws", "azure", "google cloud", "<PERSON><PERSON>", "digitalocean", "vps", "сервер", "хостинг", "домен", "ssl", "https", "безопасность", "pytest", "selenium", "ci/cd", "devops", "ansible", "terraform", "kubernetes", "мобильное приложение", "android", "ios", "flutter", "react native", "kotlin", "swift", "java", "c#", "c++", "go", "rust", "ruby", "rails", "машинное обучение", "ml", "ai", "искусственный интеллект", "нейронные сети", "tensorflow", "pytorch", "opencv", "pandas", "numpy", "scipy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jup<PERSON><PERSON>", "data science", "анализ данных", "big data", "elasticsearch", "kibana", "grafana", "prometheus", "blockchain", "криптовалюта", "smart contract", "solidity", "web3", "defi", "nft"], "excluded_keywords": ["только для женщин", "только девушки", "женский", "модель", "фото", "видео", "контент для взрослых", "18+", "эротика", "порно", "казино", "ставки", "азартные игры", "форекс", "бинарные опционы", "пирамида", "мошенничество", "обман", "развод", "лохотрон", "спам", "рассылка", "накрутка", "лайки", "подписчики", "боты", "фейк", "fake", "только Москва", "только СПб", "только офис", "переезд", "релокация", "студент", "ста<PERSON><PERSON>р", "без опыта", "бесплатно", "за идею", "за процент", "партнерство", "инвестиции", "вложения", "дизайн логотипа", "переработка логотипа", "видеомонтаж", "монтаж видео", "озвучка", "переводчик", "копи<PERSON><PERSON><PERSON><PERSON><PERSON>р", "smm", "маркетинг-кит", "презентация", "3д модель", "blender", "фигма", "figma дизайн", "карточки товаров", "креативы", "рил<PERSON>"], "min_budget": 5000, "max_budget": 200000, "use_regex": false, "case_sensitive": false}, "database": {"file": "projects.db"}, "logging": {"level": "INFO", "file": "bot.log", "max_size_mb": 10, "backup_count": 5}, "notifications": {"include_description": true, "max_description_length": 200, "send_summary": true, "summary_interval": 3600}}