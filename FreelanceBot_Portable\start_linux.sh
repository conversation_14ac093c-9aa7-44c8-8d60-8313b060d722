#!/bin/bash

echo "========================================"
echo "    🚀 Freelance Bot v1.0"
echo "========================================"
echo

# Проверяем Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 не установлен!"
    echo "   Установите: sudo apt install python3 python3-pip"
    exit 1
fi

echo "✅ Python3 найден"
echo

# Проверяем pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 не установлен!"
    echo "   Установите: sudo apt install python3-pip"
    exit 1
fi

echo "✅ pip3 найден"
echo

# Устанавливаем зависимости
echo "📦 Установка зависимостей..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Ошибка установки зависимостей!"
    exit 1
fi

echo "✅ Зависимости установлены"
echo

# Проверяем конфигурацию
if [ ! -f config.json ]; then
    echo "❌ Файл config.json не найден!"
    echo "   Скопируйте config.example.json в config.json"
    echo "   И настройте telegram_token и telegram_chat_id"
    exit 1
fi

echo "✅ Конфигурация найдена"
echo

# Запускаем бота
echo "🔄 Запуск бота..."
echo "   Для остановки нажмите Ctrl+C"
echo
python3 main.py

echo
echo "⚠️ Бот остановлен"
