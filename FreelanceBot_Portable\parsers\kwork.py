from typing import List, Dict, Any
from bs4 import BeautifulSoup
from . import BaseParser

class KworkParser(BaseParser):
    """Парсер для Kwork.ru"""
    
    def __init__(self, logger=None):
        super().__init__(
            name="K<PERSON>",
            base_url="https://kwork.ru",
            logger=logger
        )
    
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с Kwork.ru"""
        projects = []
        
        try:
            # Основная страница с проектами (раздел "Хочу")
            url = f"{self.base_url}/projects"
            content = await self._make_request(url)
            
            if not content:
                self.log_warning("Не удалось получить содержимое страницы")
                return projects
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Ищем карточки проектов
            project_cards = soup.find_all('div', class_='wants-card')
            if not project_cards:
                project_cards = soup.find_all('div', class_='project-item')
            if not project_cards:
                project_cards = soup.find_all('article', class_='want')
            
            self.log_info(f"Найдено {len(project_cards)} карточек проектов")
            
            for card in project_cards:
                try:
                    project = await self._parse_project_card(card)
                    if project:
                        projects.append(project)
                except Exception as e:
                    self.log_error(f"Ошибка при обработке карточки проекта: {e}")
            
            self.log_info(f"Успешно обработано {len(projects)} проектов")
            
        except Exception as e:
            self.log_error(f"Ошибка при парсинге Kwork: {e}", exc_info=True)
        
        return projects
    
    async def _parse_project_card(self, card) -> Dict[str, Any]:
        """Парсит отдельную карточку проекта"""
        project = {
            'platform': 'Kwork',
            'source': 'Kwork'
        }
        
        # Извлекаем заголовок и ссылку
        title_elem = card.find('h3', class_='wants-card__header-title')
        if not title_elem:
            title_elem = card.find('h2', class_='want-card__title')
        if not title_elem:
            title_elem = card.find('a', class_='want-card__link')
        
        if title_elem:
            link_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem
            
            if link_elem:
                project['title'] = self._clean_text(link_elem.get_text())
                href = link_elem.get('href')
                if href:
                    if href.startswith('/'):
                        project['url'] = f"{self.base_url}{href}"
                    else:
                        project['url'] = href
                    project['id'] = self._extract_project_id(project['url'])
            else:
                project['title'] = self._clean_text(title_elem.get_text())
        
        if not project.get('title'):
            return None
        
        # Извлекаем бюджет
        budget_elem = card.find('div', class_='wants-card__price')
        if not budget_elem:
            budget_elem = card.find('span', class_='price')
        if not budget_elem:
            budget_elem = card.find('div', class_='want-card__budget')
        
        if budget_elem:
            budget_text = self._clean_text(budget_elem.get_text())
            project['budget'] = self._parse_budget(budget_text)
            project['budget_text'] = budget_text
        else:
            project['budget'] = 0
            project['budget_text'] = ""
        
        # Извлекаем описание
        desc_elem = card.find('div', class_='wants-card__description-text')
        if not desc_elem:
            desc_elem = card.find('div', class_='want-card__description')
        if not desc_elem:
            desc_elem = card.find('p', class_='description')
        
        if desc_elem:
            project['description'] = self._clean_text(desc_elem.get_text())
        else:
            project['description'] = ""
        
        # Извлекаем дату
        date_elem = card.find('time')
        if not date_elem:
            date_elem = card.find('span', class_='wants-card__date')
        if not date_elem:
            date_elem = card.find('div', class_='date')
        
        if date_elem:
            project['published_date'] = self._clean_text(date_elem.get_text())
        
        # Извлекаем категорию
        category_elem = card.find('a', class_='wants-card__category')
        if not category_elem:
            category_elem = card.find('span', class_='category')
        
        if category_elem:
            project['category'] = self._clean_text(category_elem.get_text())
        
        # Извлекаем информацию о заказчике
        customer_elem = card.find('div', class_='wants-card__user')
        if not customer_elem:
            customer_elem = card.find('a', class_='customer')
        
        if customer_elem:
            customer_name = customer_elem.find('span', class_='username')
            if customer_name:
                project['customer'] = self._clean_text(customer_name.get_text())
            else:
                project['customer'] = self._clean_text(customer_elem.get_text())
        
        # Извлекаем количество откликов
        responses_elem = card.find('span', class_='wants-card__offers-count')
        if not responses_elem:
            responses_elem = card.find('div', class_='responses')
        
        if responses_elem:
            responses_text = self._clean_text(responses_elem.get_text())
            project['responses_count'] = self._extract_number(responses_text)
        
        # Извлекаем срок выполнения
        deadline_elem = card.find('span', class_='wants-card__deadline')
        if deadline_elem:
            project['deadline'] = self._clean_text(deadline_elem.get_text())
        
        # Извлекаем теги/навыки
        tags_container = card.find('div', class_='wants-card__tags')
        if tags_container:
            tags = []
            tag_elements = tags_container.find_all('span', class_='tag')
            for tag_elem in tag_elements:
                tag_text = self._clean_text(tag_elem.get_text())
                if tag_text and tag_text not in tags:
                    tags.append(tag_text)
            project['tags'] = tags
        
        # Извлекаем статус проекта
        status_elem = card.find('span', class_='wants-card__status')
        if status_elem:
            project['status'] = self._clean_text(status_elem.get_text())
        
        # Проверяем обязательные поля
        if not project.get('title'):
            return None
        
        # Если нет ID, генерируем его из заголовка
        if not project.get('id'):
            import hashlib
            project['id'] = hashlib.md5(project['title'].encode()).hexdigest()[:8]
        
        return project
    
    def _extract_number(self, text: str) -> int:
        """Извлекает число из текста"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _parse_budget(self, budget_text: str) -> int:
        """Парсит бюджет специфично для Kwork"""
        if not budget_text:
            return 0
        
        budget_text = budget_text.lower().replace(' ', '')
        
        # Удаляем валютные символы
        budget_text = budget_text.replace('₽', '').replace('руб', '').replace('рублей', '')
        budget_text = budget_text.replace('$', '').replace('usd', '').replace('долларов', '')
        
        # Обрабатываем специфичные для Kwork форматы
        if 'договорная' in budget_text or 'договор' in budget_text:
            return 0
        
        if 'до' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text.split('до')[1])
            if numbers:
                return int(numbers[0])
        
        if 'от' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text.split('от')[1])
            if numbers:
                return int(numbers[0])
        
        # Обычное извлечение числа
        return super()._parse_budget(budget_text)
