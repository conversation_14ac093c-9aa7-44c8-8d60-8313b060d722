from typing import List, Dict, Any
from bs4 import BeautifulSoup
from . import BaseParser

class FlRuParser(BaseParser):
    """Парсер для FL.ru"""
    
    def __init__(self, logger=None):
        super().__init__(
            name="FL.ru",
            base_url="https://www.fl.ru",
            logger=logger
        )
    
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с FL.ru"""
        projects = []
        
        try:
            # Основная страница с проектами
            url = f"{self.base_url}/projects/"
            content = await self._make_request(url)
            
            if not content:
                self.log_warning("Не удалось получить содержимое страницы")
                return projects
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Ищем карточки проектов
            project_cards = soup.find_all('div', class_='b-post')
            
            if not project_cards:
                # Пробуем альтернативные селекторы
                project_cards = soup.find_all('article', class_='project')
                if not project_cards:
                    project_cards = soup.find_all('div', {'data-project-id': True})
            
            self.log_info(f"Найдено {len(project_cards)} карточек проектов")
            
            for card in project_cards:
                try:
                    project = await self._parse_project_card(card)
                    if project:
                        projects.append(project)
                except Exception as e:
                    self.log_error(f"Ошибка при обработке карточки проекта: {e}")
            
            self.log_info(f"Успешно обработано {len(projects)} проектов")
            
        except Exception as e:
            self.log_error(f"Ошибка при парсинге FL.ru: {e}", exc_info=True)
        
        return projects
    
    async def _parse_project_card(self, card) -> Dict[str, Any]:
        """Парсит отдельную карточку проекта"""
        project = {
            'platform': 'FL.ru',
            'source': 'FL.ru'
        }
        
        # Извлекаем заголовок
        title_elem = card.find('h2', class_='b-post__title')
        if not title_elem:
            title_elem = card.find('h3', class_='b-post__title')
        if not title_elem:
            title_elem = card.find('a', class_='b-post__title-link')
        
        if title_elem:
            project['title'] = self._clean_text(title_elem.get_text())
            
            # Извлекаем ссылку
            link_elem = title_elem.find('a')
            if link_elem and link_elem.get('href'):
                href = link_elem['href']
                if href.startswith('/'):
                    project['url'] = f"{self.base_url}{href}"
                else:
                    project['url'] = href
                project['id'] = self._extract_project_id(project['url'])
        else:
            # Если не нашли заголовок, пропускаем проект
            return None
        
        # Извлекаем бюджет
        budget_elem = card.find('div', class_='b-post__price')
        if not budget_elem:
            budget_elem = card.find('span', class_='b-post__price')
        if not budget_elem:
            budget_elem = card.find('div', class_='price')
        
        if budget_elem:
            budget_text = self._clean_text(budget_elem.get_text())
            project['budget'] = self._parse_budget(budget_text)
            project['budget_text'] = budget_text
        else:
            project['budget'] = 0
            project['budget_text'] = ""
        
        # Извлекаем описание
        desc_elem = card.find('div', class_='b-post__body')
        if not desc_elem:
            desc_elem = card.find('div', class_='b-post__txt')
        if not desc_elem:
            desc_elem = card.find('p', class_='description')
        
        if desc_elem:
            project['description'] = self._clean_text(desc_elem.get_text())
        else:
            project['description'] = ""
        
        # Извлекаем дату публикации
        date_elem = card.find('time')
        if not date_elem:
            date_elem = card.find('span', class_='b-post__date')
        if not date_elem:
            date_elem = card.find('div', class_='date')
        
        if date_elem:
            project['published_date'] = self._clean_text(date_elem.get_text())
        
        # Извлекаем категорию
        category_elem = card.find('a', class_='b-post__category')
        if not category_elem:
            category_elem = card.find('span', class_='category')
        
        if category_elem:
            project['category'] = self._clean_text(category_elem.get_text())
        
        # Извлекаем информацию о заказчике
        customer_elem = card.find('a', class_='b-post__customer')
        if not customer_elem:
            customer_elem = card.find('span', class_='customer')
        
        if customer_elem:
            project['customer'] = self._clean_text(customer_elem.get_text())
        
        # Извлекаем количество откликов
        responses_elem = card.find('span', class_='b-post__responses-count')
        if not responses_elem:
            responses_elem = card.find('div', class_='responses')
        
        if responses_elem:
            responses_text = self._clean_text(responses_elem.get_text())
            project['responses_count'] = self._extract_number(responses_text)
        
        # Извлекаем теги/навыки
        tags_container = card.find('div', class_='b-post__tags')
        if tags_container:
            tags = []
            tag_elements = tags_container.find_all('a')
            for tag_elem in tag_elements:
                tag_text = self._clean_text(tag_elem.get_text())
                if tag_text:
                    tags.append(tag_text)
            project['tags'] = tags
        
        # Проверяем обязательные поля
        if not project.get('title') or not project.get('id'):
            return None
        
        return project
    
    def _extract_number(self, text: str) -> int:
        """Извлекает число из текста"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _parse_budget(self, budget_text: str) -> int:
        """Парсит бюджет специфично для FL.ru"""
        if not budget_text:
            return 0

        budget_text_original = budget_text
        budget_text_clean = budget_text.lower().replace(' ', '').replace('\n', '').replace('\t', '')

        # Проверяем на "по договоренности" и похожие фразы
        negotiable_phrases = [
            'подоговоренности', 'подоговорённости', 'договорная', 'договорнаяцена',
            'обсуждается', 'обсуждаемо', 'поусловиям', 'условная', 'неуказан',
            'неопределен', 'неопределён', 'тбд', 'tbd', 'договор'
        ]

        for phrase in negotiable_phrases:
            if phrase in budget_text_clean:
                return -1  # Специальное значение для "по договоренности"

        # Ищем рубли в первую очередь (приоритет рублям)
        import re

        # Паттерны для поиска рублей
        ruble_patterns = [
            r'(\d+(?:\s*\d+)*)\s*₽',  # 15 000 ₽
            r'(\d+(?:\s*\d+)*)\s*р\.?',  # 15 000 Р или 15 000 р.
            r'(\d+(?:\s*\d+)*)\s*руб',  # 15 000 руб
            r'(\d+(?:\s*\d+)*)\s*рублей',  # 15 000 рублей
        ]

        for pattern in ruble_patterns:
            match = re.search(pattern, budget_text, re.IGNORECASE)
            if match:
                # Убираем пробелы из числа и конвертируем
                number_str = match.group(1).replace(' ', '')
                try:
                    return int(number_str)
                except ValueError:
                    continue

        # Если рублей не нашли, ищем доллары и конвертируем
        dollar_patterns = [
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*\$',  # 190.80 $
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*usd',  # 190 USD
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*долларов',  # 190 долларов
        ]

        for pattern in dollar_patterns:
            match = re.search(pattern, budget_text, re.IGNORECASE)
            if match:
                number_str = match.group(1).replace(' ', '')
                try:
                    dollars = float(number_str)
                    # Конвертируем доллары в рубли (примерный курс 100 руб за доллар)
                    return int(dollars * 100)
                except ValueError:
                    continue

        # Если долларов не нашли, ищем евро и конвертируем
        euro_patterns = [
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*€',  # 168.06 €
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*eur',  # 168 EUR
            r'(\d+(?:\s*\d+)*(?:\.\d+)?)\s*евро',  # 168 евро
        ]

        for pattern in euro_patterns:
            match = re.search(pattern, budget_text, re.IGNORECASE)
            if match:
                number_str = match.group(1).replace(' ', '')
                try:
                    euros = float(number_str)
                    # Конвертируем евро в рубли (примерный курс 110 руб за евро)
                    return int(euros * 110)
                except ValueError:
                    continue

        # Обрабатываем диапазоны (если ничего конкретного не нашли)
        budget_text_clean = budget_text_clean.replace('₽', '').replace('руб', '').replace('рублей', '')
        budget_text_clean = budget_text_clean.replace('$', '').replace('usd', '').replace('долларов', '')
        budget_text_clean = budget_text_clean.replace('€', '').replace('eur', '').replace('евро', '')

        if 'от' in budget_text_clean and 'до' in budget_text_clean:
            # Берем минимальное значение из диапазона
            numbers = re.findall(r'\d+', budget_text_clean)
            if len(numbers) >= 2:
                return int(numbers[0])
        elif 'от' in budget_text_clean:
            numbers = re.findall(r'\d+', budget_text_clean.split('от')[1])
            if numbers:
                return int(numbers[0])
        elif 'до' in budget_text_clean:
            numbers = re.findall(r'\d+', budget_text_clean.split('до')[1])
            if numbers:
                return int(numbers[0])

        # Обычное извлечение числа (последний шанс)
        numbers = re.findall(r'\d+', budget_text_clean)
        return int(numbers[0]) if numbers else 0
