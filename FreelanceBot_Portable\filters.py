import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

class FilterManager:
    """Класс для управления фильтрацией проектов"""
    
    def __init__(self, config, logger=None):
        self.config = config
        self.logger = logger
    
    def should_notify(self, project: Dict[str, Any]) -> tuple[bool, str]:
        """
        Определяет, нужно ли отправлять уведомление о проекте
        
        Args:
            project: Данные проекта
            
        Returns:
            Tuple (should_notify, reason)
        """
        
        # Проверка бюджета
        budget_check, budget_reason = self._check_budget(project)
        if not budget_check:
            if self.logger:
                self.logger.log_filter_reject(project, budget_reason)
            return False, budget_reason
        
        # Проверка ключевых слов
        keyword_check, keyword_reason = self._check_keywords(project)
        if not keyword_check:
            if self.logger:
                self.logger.log_filter_reject(project, keyword_reason)
            return False, keyword_reason
        
        # Проверка исключающих слов
        excluded_check, excluded_reason = self._check_excluded_keywords(project)
        if not excluded_check:
            if self.logger:
                self.logger.log_filter_reject(project, excluded_reason)
            return False, excluded_reason
        
        # Проверка дубликатов по заголовку
        duplicate_check, duplicate_reason = self._check_duplicates(project)
        if not duplicate_check:
            if self.logger:
                self.logger.log_filter_reject(project, duplicate_reason)
            return False, duplicate_reason
        
        # Все проверки пройдены
        reason = f"Прошел все фильтры: бюджет {project.get('budget', 0)}, найдены ключевые слова"
        if self.logger:
            self.logger.log_filter_match(project, reason)
        return True, reason
    
    def _check_budget(self, project: Dict[str, Any]) -> tuple[bool, str]:
        """Проверяет соответствие бюджета"""
        budget = project.get('budget', 0)

        # Убираем все ограничения по бюджету - принимаем любой бюджет
        return True, f"Бюджет {budget} принят (без ограничений)"
    
    def _check_keywords(self, project: Dict[str, Any]) -> tuple[bool, str]:
        """Проверяет наличие ключевых слов"""
        keywords = self.config.keywords
        if not keywords:
            return True, "Ключевые слова не заданы"

        title = project.get('title', '').lower()
        description = project.get('description', '').lower()
        text_to_search = f"{title} {description}"

        use_regex = self.config.get('filters.use_regex', False)
        case_sensitive = self.config.get('filters.case_sensitive', False)

        if not case_sensitive:
            text_to_search = text_to_search.lower()

        found_keywords = []

        for keyword in keywords:
            if not case_sensitive:
                keyword = keyword.lower()

            if use_regex:
                try:
                    pattern = re.compile(keyword, re.IGNORECASE if not case_sensitive else 0)
                    if pattern.search(text_to_search):
                        found_keywords.append(keyword)
                except re.error:
                    # Если регулярное выражение некорректно, используем обычный поиск
                    if keyword in text_to_search:
                        found_keywords.append(keyword)
            else:
                if keyword in text_to_search:
                    found_keywords.append(keyword)

        if found_keywords:
            return True, f"Найдены ключевые слова: {', '.join(found_keywords)}"
        else:
            return False, "Ключевые слова не найдены"
    
    def _check_excluded_keywords(self, project: Dict[str, Any]) -> tuple[bool, str]:
        """Проверяет отсутствие исключающих слов"""
        excluded_keywords = self.config.excluded_keywords
        if not excluded_keywords:
            return True, "Исключающие слова не заданы"

        title = project.get('title', '').lower()
        description = project.get('description', '').lower()
        text_to_search = f"{title} {description}"

        case_sensitive = self.config.get('filters.case_sensitive', False)
        if not case_sensitive:
            text_to_search = text_to_search.lower()

        found_excluded = []

        for keyword in excluded_keywords:
            if not case_sensitive:
                keyword = keyword.lower()

            if keyword in text_to_search:
                found_excluded.append(keyword)

        if found_excluded:
            return False, f"Найдены исключающие слова: {', '.join(found_excluded)}"
        else:
            return True, "Исключающие слова не найдены"
    
    def _check_duplicates(self, project: Dict[str, Any]) -> tuple[bool, str]:
        """Проверяет дубликаты по заголовку (простая проверка)"""
        # Эта проверка может быть расширена для работы с базой данных
        title = project.get('title', '').strip()
        if len(title) < 10:
            return False, "Слишком короткий заголовок"
        
        # Проверка на подозрительные паттерны
        suspicious_patterns = [
            r'^.{1,5}$',  # Очень короткие заголовки
            r'^\s*$',     # Пустые заголовки
            r'^test\s*$', # Тестовые заголовки
        ]
        
        for pattern in suspicious_patterns:
            if re.match(pattern, title, re.IGNORECASE):
                return False, f"Подозрительный заголовок: {title}"
        
        return True, "Заголовок прошел проверку"
    
    def get_filter_summary(self) -> Dict[str, Any]:
        """Возвращает сводку по настройкам фильтров"""
        return {
            'keywords': self.config.keywords,
            'excluded_keywords': self.config.excluded_keywords,
            'min_budget': self.config.min_budget,
            'max_budget': self.config.max_budget,
            'use_regex': self.config.get('filters.use_regex', False),
            'case_sensitive': self.config.get('filters.case_sensitive', False),
            'enabled_platforms': self.config.enabled_platforms
        }
    
    def validate_regex_keyword(self, keyword: str) -> tuple[bool, str]:
        """Проверяет корректность регулярного выражения"""
        try:
            re.compile(keyword)
            return True, "Регулярное выражение корректно"
        except re.error as e:
            return False, f"Ошибка в регулярном выражении: {str(e)}"

    def get_keyword_statistics(self, projects: List[Dict[str, Any]]) -> Dict[str, int]:
        """Возвращает статистику по ключевым словам"""
        keyword_stats = {}
        
        for keyword in self.config.keywords:
            count = 0
            for project in projects:
                title = project.get('title', '').lower()
                description = project.get('description', '').lower()
                text = f"{title} {description}"
                
                if keyword.lower() in text:
                    count += 1
            
            keyword_stats[keyword] = count
        
        return keyword_stats
