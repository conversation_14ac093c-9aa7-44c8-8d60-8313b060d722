# 🚀 Инструкция по настройке бота

## ❌ Ошибки, которые вы видите:

### 1. `'Config' object has no attribute 'config'`
✅ **ИСПРАВЛЕНО** - обновлен файл `config.py`

### 2. `Token must contain a colon`
❌ **ТРЕБУЕТ НАСТРОЙКИ** - нужно указать реальный токен бота

## 🔧 Пошаговая настройка:

### Шаг 1: Создание Telegram бота

1. **Найдите @BotFather в Telegram**
   - Откройте Telegram
   - Найдите бота `@BotFather`
   - Нажмите "Start"

2. **Создайте нового бота**
   ```
   /newbot
   ```
   
3. **Укажите имя бота**
   ```
   Freelance Monitor Bot
   ```
   
4. **Укажите username бота** (должен заканчиваться на "bot")
   ```
   freelance_monitor_bot
   ```
   
5. **Скопируйте токен**
   BotFather пришлет вам токен вида:
   ```
   1234567890:ABCdefGHIjklMNOpqrsTUVwxyz123456789
   ```

### Шаг 2: Получение Chat ID

1. **Найдите @userinfobot в Telegram**
2. **Отправьте любое сообщение**
3. **Скопируйте ваш ID** (например: `123456789`)

### Шаг 3: Настройка конфигурации

Отредактируйте файл `config.json`:

```json
{
  "telegram": {
    "token": "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz123456789",
    "chat_id": "123456789",
    "admin_ids": []
  }
}
```

**Замените:**
- `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz123456789` на ваш реальный токен
- `123456789` на ваш реальный Chat ID

### Шаг 4: Запуск бота

```bash
python main.py
```

## ✅ Правильный вывод при запуске:

```
🤖 Бот запущен!
⏱ Интервал проверки: 60 секунд
🌐 Активных платформ: 8
🔍 Ключевых слов: 10
💰 Минимальный бюджет: 3000 ₽
💎 Максимальный бюджет: не ограничен
📱 Запуск Telegram бота...
```

## 🔍 Проверка работы:

1. **Найдите вашего бота в Telegram**
2. **Отправьте команду `/start`**
3. **Проверьте ответ бота**
4. **Попробуйте команду `/status`**

## ⚠️ Возможные проблемы:

### Проблема: "Unauthorized"
**Решение:** Проверьте правильность токена

### Проблема: "Chat not found"
**Решение:** Проверьте правильность Chat ID

### Проблема: Бот не отвечает
**Решение:** 
1. Убедитесь, что бот запущен
2. Проверьте, что вы написали правильному боту
3. Попробуйте команду `/start`

## 📝 Пример правильного config.json:

```json
{
  "telegram": {
    "token": "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz123456789",
    "chat_id": "123456789",
    "admin_ids": []
  },
  "monitoring": {
    "check_interval": 60,
    "enabled_platforms": [
      "fl_ru",
      "freelance_ru",
      "habr_freelance",
      "kwork"
    ]
  },
  "filters": {
    "keywords": [
      "python",
      "telegram",
      "бот",
      "django"
    ],
    "excluded_keywords": [
      "дизайн",
      "логотип"
    ],
    "min_budget": 5000,
    "max_budget": 100000,
    "use_regex": false,
    "case_sensitive": false
  }
}
```

## 🎯 После успешного запуска:

1. **Настройте фильтры через Telegram:**
   ```
   /add_keyword python
   /set_min_budget 5000
   /set_max_budget 50000
   ```

2. **Проверьте статус:**
   ```
   /status
   ```

3. **Запустите тестовую проверку:**
   ```
   /check
   ```

**Готово! Бот будет отправлять уведомления о новых фриланс-заказах! 🚀**
