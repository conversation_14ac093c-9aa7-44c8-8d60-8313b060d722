from typing import List, Dict, Any
from bs4 import BeautifulSoup
from . import BaseParser

class HabrFreelanceParser(BaseParser):
    """Парсер для Habr Freelance"""
    
    def __init__(self, logger=None):
        super().__init__(
            name="Habr Freelance",
            base_url="https://freelance.habr.com",
            logger=logger
        )
    
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с Habr Freelance"""
        projects = []
        
        try:
            # Основная страница с проектами
            url = f"{self.base_url}/tasks"
            content = await self._make_request(url)
            
            if not content:
                self.log_warning("Не удалось получить содержимое страницы")
                return projects
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Ищем карточки проектов
            project_cards = soup.find_all('div', class_='task')
            if not project_cards:
                project_cards = soup.find_all('article', class_='task-item')
            if not project_cards:
                project_cards = soup.find_all('div', {'data-task-id': True})
            
            self.log_info(f"Найдено {len(project_cards)} карточек проектов")
            
            for card in project_cards:
                try:
                    project = await self._parse_project_card(card)
                    if project:
                        projects.append(project)
                except Exception as e:
                    self.log_error(f"Ошибка при обработке карточки проекта: {e}")
            
            self.log_info(f"Успешно обработано {len(projects)} проектов")
            
        except Exception as e:
            self.log_error(f"Ошибка при парсинге Habr Freelance: {e}", exc_info=True)
        
        return projects
    
    async def _parse_project_card(self, card) -> Dict[str, Any]:
        """Парсит отдельную карточку проекта"""
        project = {
            'platform': 'Habr Freelance',
            'source': 'Habr Freelance'
        }
        
        # Извлекаем ID из data-атрибута
        task_id = card.get('data-task-id')
        if task_id:
            project['id'] = task_id
        
        # Извлекаем заголовок и ссылку
        title_elem = card.find('h2', class_='task__title')
        if not title_elem:
            title_elem = card.find('h3', class_='task__title')
        if not title_elem:
            title_elem = card.find('a', class_='task__title-link')
        
        if title_elem:
            link_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem
            
            if link_elem:
                project['title'] = self._clean_text(link_elem.get_text())
                href = link_elem.get('href')
                if href:
                    if href.startswith('/'):
                        project['url'] = f"{self.base_url}{href}"
                    else:
                        project['url'] = href
                    if not project.get('id'):
                        project['id'] = self._extract_project_id(project['url'])
            else:
                project['title'] = self._clean_text(title_elem.get_text())
        
        if not project.get('title'):
            return None
        
        # Извлекаем бюджет
        budget_elem = card.find('div', class_='task__budget')
        if not budget_elem:
            budget_elem = card.find('span', class_='budget')
        if not budget_elem:
            budget_elem = card.find('div', class_='price')
        
        if budget_elem:
            budget_text = self._clean_text(budget_elem.get_text())
            project['budget'] = self._parse_budget(budget_text)
            project['budget_text'] = budget_text
        else:
            project['budget'] = 0
            project['budget_text'] = ""
        
        # Извлекаем описание
        desc_elem = card.find('div', class_='task__description')
        if not desc_elem:
            desc_elem = card.find('div', class_='task__text')
        if not desc_elem:
            desc_elem = card.find('p', class_='description')
        
        if desc_elem:
            project['description'] = self._clean_text(desc_elem.get_text())
        else:
            project['description'] = ""
        
        # Извлекаем дату
        date_elem = card.find('time', class_='task__date')
        if not date_elem:
            date_elem = card.find('span', class_='date')
        if not date_elem:
            date_elem = card.find('div', class_='task__meta-date')
        
        if date_elem:
            project['published_date'] = self._clean_text(date_elem.get_text())
        
        # Извлекаем теги/навыки
        tags_container = card.find('div', class_='task__tags')
        if not tags_container:
            tags_container = card.find('div', class_='tags')
        
        if tags_container:
            tags = []
            tag_elements = tags_container.find_all('a', class_='tag')
            if not tag_elements:
                tag_elements = tags_container.find_all('span', class_='tag')
            
            for tag_elem in tag_elements:
                tag_text = self._clean_text(tag_elem.get_text())
                if tag_text and tag_text not in tags:
                    tags.append(tag_text)
            project['tags'] = tags
        
        # Извлекаем информацию о заказчике
        customer_elem = card.find('a', class_='task__customer')
        if not customer_elem:
            customer_elem = card.find('div', class_='customer')
        
        if customer_elem:
            project['customer'] = self._clean_text(customer_elem.get_text())
        
        # Извлекаем количество откликов
        responses_elem = card.find('span', class_='task__responses-count')
        if not responses_elem:
            responses_elem = card.find('div', class_='responses')
        
        if responses_elem:
            responses_text = self._clean_text(responses_elem.get_text())
            project['responses_count'] = self._extract_number(responses_text)
        
        # Извлекаем тип задачи
        type_elem = card.find('span', class_='task__type')
        if type_elem:
            project['task_type'] = self._clean_text(type_elem.get_text())
        
        # Извлекаем срок выполнения
        deadline_elem = card.find('span', class_='task__deadline')
        if deadline_elem:
            project['deadline'] = self._clean_text(deadline_elem.get_text())
        
        # Извлекаем рейтинг заказчика
        rating_elem = card.find('span', class_='customer__rating')
        if rating_elem:
            project['customer_rating'] = self._clean_text(rating_elem.get_text())
        
        # Проверяем обязательные поля
        if not project.get('title'):
            return None
        
        # Если нет ID, генерируем его из заголовка
        if not project.get('id'):
            import hashlib
            project['id'] = hashlib.md5(project['title'].encode()).hexdigest()[:8]
        
        return project
    
    def _extract_number(self, text: str) -> int:
        """Извлекает число из текста"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _parse_budget(self, budget_text: str) -> int:
        """Парсит бюджет специфично для Habr Freelance"""
        if not budget_text:
            return 0
        
        budget_text = budget_text.lower().replace(' ', '')
        
        # Удаляем валютные символы
        budget_text = budget_text.replace('₽', '').replace('руб', '').replace('рублей', '')
        budget_text = budget_text.replace('$', '').replace('usd', '').replace('долларов', '')
        budget_text = budget_text.replace('€', '').replace('eur', '').replace('евро', '')
        
        # Обрабатываем специфичные для Habr форматы
        if 'договорная' in budget_text or 'договор' in budget_text:
            return 0
        
        if 'почасовая' in budget_text or '/час' in budget_text:
            # Пытаемся извлечь почасовую ставку
            import re
            numbers = re.findall(r'\d+', budget_text)
            if numbers:
                hourly_rate = int(numbers[0])
                # Предполагаем 40 часов работы для оценки общего бюджета
                return hourly_rate * 40
        
        # Обрабатываем диапазоны
        if '—' in budget_text or '-' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text)
            if len(numbers) >= 2:
                # Берем минимальное значение из диапазона
                return int(numbers[0])
        
        # Обычное извлечение числа
        return super()._parse_budget(budget_text)
