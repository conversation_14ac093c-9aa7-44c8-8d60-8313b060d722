# 🚀 Freelance Bot v1.0 - Портативная версия

## 📋 Быстрый старт

### Windows:
1. Запустите `start_windows.bat`
2. Следуйте инструкциям на экране

### Linux/Mac:
1. Запустите `./start_linux.sh`
2. Следуйте инструкциям на экране

## ⚙️ Ручная настройка

### 1️⃣ Установка Python
- **Windows**: Скачайте с https://python.org (минимум Python 3.8)
- **Linux**: `sudo apt install python3 python3-pip`
- **Mac**: `brew install python3`

### 2️⃣ Установка зависимостей
```bash
pip install -r requirements.txt
```

### 3️⃣ Настройка конфигурации
1. Скопируйте `config.example.json` в `config.json`
2. Получите токен бота у @BotFather в Telegram
3. Узнайте ваш chat_id у @userinfobot
4. Отредактируйте `config.json`:
```json
{
  "telegram_token": "ВАШ_ТОКЕН_БОТА",
  "telegram_chat_id": "ВАШ_CHAT_ID"
}
```

### 4️⃣ Запуск
```bash
python main.py
```

## 🔧 Настройки

### Основные параметры в config.json:
- `telegram_token` - токен бота от @BotFather
- `telegram_chat_id` - ID чата для уведомлений
- `check_interval` - интервал проверки в секундах (60)
- `min_budget` - минимальный бюджет проектов
- `max_budget` - максимальный бюджет проектов

### Фильтры:
- `keywords` - ключевые слова для поиска
- `exclude_keywords` - исключающие слова

## 📊 Мониторинг платформ

Бот отслеживает проекты на:
- ✅ FL.ru (~29 проектов за цикл)
- ✅ Freelance.ru (~50 проектов за цикл)
- ✅ Weblancer (~87 проектов за цикл)
- ⚠️ Habr Freelance (временно недоступен)
- ⚠️ Upwork (требует настройку)
- ⚠️ Freelansim (временно недоступен)
- ⚠️ Kwork (нет активных проектов)
- ⚠️ YouDo (нет активных проектов)

**Итого: ~166 проектов за цикл мониторинга**

## 📱 Команды Telegram бота

- `/start` - Запуск бота
- `/help` - Справка по командам
- `/stats` - Статистика работы
- `/diagnose` - Диагностика платформ

## 🖥️ Развертывание на сервере

### VPS/Dedicated сервер:
1. Загрузите папку на сервер
2. Установите Python 3.8+
3. Настройте config.json
4. Запустите бота

### Автозапуск (Linux):
Создайте systemd сервис:
```bash
sudo nano /etc/systemd/system/freelancebot.service
```

Содержимое:
```ini
[Unit]
Description=Freelance Bot
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/FreelanceBot_Portable
ExecStart=/usr/bin/python3 main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

Активация:
```bash
sudo systemctl enable freelancebot
sudo systemctl start freelancebot
```

## 📝 Логи

Бот создает файл `bot.log` с подробными логами работы.
Следите за логами для диагностики проблем.

## 🆘 Поддержка

При проблемах проверьте:
1. Правильность токена в config.json
2. Доступность интернета
3. Логи в файле bot.log
4. Версию Python (минимум 3.8)

## 🎯 Производительность

- **Стабильность**: Высокая
- **Проектов за цикл**: ~166
- **Время цикла**: ~30 секунд
- **Эффективность**: 62.5% платформ работают

## 🎉 Готово!

Бот будет автоматически мониторить фриланс-платформы и отправлять уведомления о новых проектах!

---
**Freelance Bot v1.0** - Готов к продуктивному использованию! 🚀
