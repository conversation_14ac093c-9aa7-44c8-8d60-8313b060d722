from typing import List, Dict, Any
from bs4 import BeautifulSoup
from . import BaseParser

class UpworkParser(BaseParser):
    """Парсер для Upwork (международная площадка)"""
    
    def __init__(self, logger=None):
        super().__init__(
            name="Upwork",
            base_url="https://www.upwork.com",
            logger=logger
        )
    
    async def parse_projects(self) -> List[Dict[str, Any]]:
        """Парсит проекты с Upwork"""
        projects = []
        
        try:
            # Основная страница с проектами
            url = f"{self.base_url}/nx/search/jobs/"
            content = await self._make_request(url)
            
            if not content:
                self.log_warning("Не удалось получить содержимое страницы")
                return projects
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Ищем карточки проектов
            project_cards = soup.find_all('article', {'data-test': 'JobTile'})
            if not project_cards:
                project_cards = soup.find_all('div', class_='job-tile')
            if not project_cards:
                project_cards = soup.find_all('section', class_='up-card-section')
            
            self.log_info(f"Найдено {len(project_cards)} карточек проектов")
            
            for card in project_cards:
                try:
                    project = await self._parse_project_card(card)
                    if project:
                        projects.append(project)
                except Exception as e:
                    self.log_error(f"Ошибка при обработке карточки проекта: {e}")
            
            self.log_info(f"Успешно обработано {len(projects)} проектов")
            
        except Exception as e:
            self.log_error(f"Ошибка при парсинге Upwork: {e}", exc_info=True)
        
        return projects
    
    async def _parse_project_card(self, card) -> Dict[str, Any]:
        """Парсит отдельную карточку проекта"""
        project = {
            'platform': 'Upwork',
            'source': 'Upwork',
            'currency': 'USD'
        }
        
        # Извлекаем заголовок и ссылку
        title_elem = card.find('h4', {'data-test': 'JobTileTitle'})
        if not title_elem:
            title_elem = card.find('h3', class_='job-title')
        if not title_elem:
            title_elem = card.find('a', class_='job-title-link')
        
        if title_elem:
            link_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem
            
            if link_elem:
                project['title'] = self._clean_text(link_elem.get_text())
                href = link_elem.get('href')
                if href:
                    if href.startswith('/'):
                        project['url'] = f"{self.base_url}{href}"
                    else:
                        project['url'] = href
                    project['id'] = self._extract_project_id(project['url'])
            else:
                project['title'] = self._clean_text(title_elem.get_text())
        
        if not project.get('title'):
            return None
        
        # Извлекаем бюджет
        budget_elem = card.find('span', {'data-test': 'JobTileBudget'})
        if not budget_elem:
            budget_elem = card.find('div', class_='budget')
        if not budget_elem:
            budget_elem = card.find('span', class_='job-budget')
        
        if budget_elem:
            budget_text = self._clean_text(budget_elem.get_text())
            project['budget'] = self._parse_budget_usd(budget_text)
            project['budget_text'] = budget_text
        else:
            project['budget'] = 0
            project['budget_text'] = ""
        
        # Извлекаем описание
        desc_elem = card.find('span', {'data-test': 'JobTileDescription'})
        if not desc_elem:
            desc_elem = card.find('div', class_='job-description')
        if not desc_elem:
            desc_elem = card.find('p', class_='description')
        
        if desc_elem:
            project['description'] = self._clean_text(desc_elem.get_text())
        else:
            project['description'] = ""
        
        # Извлекаем дату
        date_elem = card.find('time')
        if not date_elem:
            date_elem = card.find('span', class_='posted-date')
        
        if date_elem:
            project['published_date'] = self._clean_text(date_elem.get_text())
        
        # Извлекаем навыки/теги
        skills_container = card.find('div', {'data-test': 'JobTileSkills'})
        if not skills_container:
            skills_container = card.find('div', class_='skills')
        
        if skills_container:
            skills = []
            skill_elements = skills_container.find_all('a')
            if not skill_elements:
                skill_elements = skills_container.find_all('span')
            
            for skill_elem in skill_elements:
                skill_text = self._clean_text(skill_elem.get_text())
                if skill_text and skill_text not in skills:
                    skills.append(skill_text)
            project['skills'] = skills
        
        # Извлекаем тип проекта
        type_elem = card.find('span', class_='job-type')
        if type_elem:
            project['job_type'] = self._clean_text(type_elem.get_text())
        
        # Извлекаем уровень опыта
        experience_elem = card.find('span', class_='experience-level')
        if experience_elem:
            project['experience_level'] = self._clean_text(experience_elem.get_text())
        
        # Извлекаем количество предложений
        proposals_elem = card.find('span', class_='proposals')
        if proposals_elem:
            proposals_text = self._clean_text(proposals_elem.get_text())
            project['proposals_count'] = self._extract_number(proposals_text)
        
        # Извлекаем информацию о клиенте
        client_elem = card.find('div', class_='client-info')
        if client_elem:
            # Рейтинг клиента
            rating_elem = client_elem.find('span', class_='client-rating')
            if rating_elem:
                project['client_rating'] = self._clean_text(rating_elem.get_text())
            
            # Потраченная сумма клиентом
            spent_elem = client_elem.find('span', class_='client-spent')
            if spent_elem:
                project['client_spent'] = self._clean_text(spent_elem.get_text())
        
        # Проверяем обязательные поля
        if not project.get('title'):
            return None
        
        # Если нет ID, генерируем его из заголовка
        if not project.get('id'):
            import hashlib
            project['id'] = hashlib.md5(project['title'].encode()).hexdigest()[:8]
        
        return project
    
    def _extract_number(self, text: str) -> int:
        """Извлекает число из текста"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else 0
    
    def _parse_budget_usd(self, budget_text: str) -> int:
        """Парсит бюджет в долларах и конвертирует в рубли"""
        if not budget_text:
            return 0
        
        budget_text = budget_text.lower().replace(' ', '').replace(',', '')
        
        # Удаляем символы доллара
        budget_text = budget_text.replace('$', '').replace('usd', '')
        
        # Обрабатываем специфичные для Upwork форматы
        if 'hourly' in budget_text or '/hr' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text)
            if numbers:
                hourly_rate = int(numbers[0])
                # Конвертируем в рубли (примерный курс 1 USD = 90 RUB)
                return hourly_rate * 40 * 90  # 40 часов * курс
        
        if 'fixed' in budget_text or 'fixed-price' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text)
            if numbers:
                fixed_price = int(numbers[0])
                return fixed_price * 90  # Конвертируем в рубли
        
        # Обрабатываем диапазоны
        if '-' in budget_text:
            import re
            numbers = re.findall(r'\d+', budget_text)
            if len(numbers) >= 2:
                min_budget = int(numbers[0])
                return min_budget * 90  # Конвертируем в рубли
        
        # Обычное извлечение числа
        import re
        numbers = re.findall(r'\d+', budget_text)
        if numbers:
            budget_usd = int(numbers[0])
            return budget_usd * 90  # Конвертируем в рубли
        
        return 0
