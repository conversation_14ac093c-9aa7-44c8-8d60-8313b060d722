# 🤖 Telegram Бот для Мониторинга Фриланс-Заказов

Автоматический мониторинг новых проектов на популярных фриланс-площадках с мгновенными уведомлениями в Telegram.

## 🌟 Особенности

- **Мониторинг 8 площадок**: FL.ru, Freelance.ru, Habr Freelance, Kwork, YouDo, Weblancer, Upwork, Freelansim
- **Мгновенные уведомления**: Получайте уведомления сразу после появления подходящих заказов
- **Умная фильтрация**: Настраиваемые фильтры по ключевым словам, бюджету и исключающим словам
- **База данных**: Автоматическое сохранение всех найденных проектов
- **Статистика**: Детальная статистика по платформам и найденным проектам
- **Telegram управление**: Полное управление ботом через команды Telegram
- **Логирование**: Подробные логи работы бота

## 📋 Поддерживаемые платформы

| Платформа | Статус | Описание |
|-----------|--------|----------|
| FL.ru | ✅ | Одна из крупнейших фриланс-бирж России |
| Freelance.ru | ✅ | Популярная площадка с большим количеством заказов |
| Habr Freelance | ✅ | Качественные заказы с хорошими бюджетами |
| Kwork | ✅ | Биржа с множеством небольших заказов |
| YouDo | ✅ | Сервис для поиска исполнителей различных задач |
| Weblancer | ✅ | Популярная биржа для веб-разработчиков |
| Upwork | ✅ | Международная площадка (английский язык) |
| Freelansim | ✅ | Новая активно развивающаяся площадка |

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
# Клонируйте репозиторий
git clone <repository-url>
cd tg_bot_freelance

# Установите зависимости
pip install -r requirements.txt
```

### 2. Создание Telegram бота

1. Найдите [@BotFather](https://t.me/BotFather) в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Сохраните полученный токен

### 3. Получение Chat ID

1. Найдите [@userinfobot](https://t.me/userinfobot) в Telegram
2. Отправьте любое сообщение
3. Скопируйте ваш Chat ID

### 4. Настройка конфигурации

При первом запуске создастся файл `config.json`. Отредактируйте его:

```json
{
  "telegram": {
    "token": "ВАШ_ТОКЕН_БОТА",
    "chat_id": "ВАШ_ЧАТ_ID"
  }
}
```

### 5. Запуск бота

```bash
python main.py
```

## ⚙️ Конфигурация

### Основные настройки

- **check_interval**: Интервал проверки в секундах (по умолчанию 60)
- **min_budget**: Минимальный бюджет для уведомлений (по умолчанию 3000 ₽)
- **keywords**: Список ключевых слов для поиска
- **excluded_keywords**: Список исключающих слов

### Пример полной конфигурации

```json
{
  "telegram": {
    "token": "YOUR_BOT_TOKEN",
    "chat_id": "YOUR_CHAT_ID"
  },
  "monitoring": {
    "check_interval": 60,
    "enabled_platforms": [
      "fl_ru",
      "freelance_ru",
      "habr_freelance",
      "kwork"
    ]
  },
  "filters": {
    "keywords": [
      "python",
      "telegram",
      "бот",
      "парсинг",
      "django",
      "fastapi"
    ],
    "excluded_keywords": [
      "дизайн",
      "логотип",
      "баннер"
    ],
    "min_budget": 5000,
    "max_budget": 100000
  }
}
```

## 📱 Команды Telegram

### Основные команды
- `/start` - Запуск бота и приветствие
- `/help` - Показать все доступные команды
- `/status` - Текущий статус бота и настроек
- `/check` - Запустить проверку вручную

### Управление ключевыми словами
- `/keywords` - Показать все ключевые слова
- `/add_keyword <слово>` - Добавить ключевое слово
- `/remove_keyword <слово>` - Удалить ключевое слово

### Управление исключениями
- `/excluded` - Показать исключающие слова
- `/add_excluded <слово>` - Добавить исключающее слово
- `/remove_excluded <слово>` - Удалить исключающее слово

### Настройка бюджета
- `/budget` - Показать текущие настройки бюджета
- `/budget <мин>` - Установить минимальный бюджет
- `/budget <мин> <макс>` - Установить диапазон бюджета
- `/set_min_budget <сумма>` - Установить минимальный бюджет
- `/set_max_budget <сумма>` - Установить максимальный бюджет
- `/remove_max_budget` - Убрать ограничение по максимальному бюджету
- `/budget_info` - Краткая информация о настройках бюджета

### Управление платформами
- `/platforms` - Показать статус всех платформ
- `/toggle <платформа>` - Включить/выключить платформу

### Статистика и информация
- `/stats [дни]` - Статистика за указанное количество дней
- `/recent [часы]` - Последние найденные проекты
- `/config` - Показать полную конфигурацию

## 💰 Примеры настройки бюджета

```bash
# Установить минимальный бюджет 5000 рублей
/set_min_budget 5000

# Установить максимальный бюджет 50000 рублей
/set_max_budget 50000

# Установить диапазон от 10000 до 100000 рублей
/budget 10000 100000

# Убрать ограничение по максимальному бюджету
/remove_max_budget

# Посмотреть текущие настройки
/budget
```

## 📊 Логирование

Бот ведет подробные логи в файле `bot.log`:
- Найденные проекты
- Отправленные уведомления
- Ошибки парсинга
- Изменения конфигурации

## 🗄️ База данных

Все найденные проекты сохраняются в SQLite базе данных `projects.db`:
- Информация о проектах
- Статистика по платформам
- История уведомлений

## 🔧 Расширенные настройки

### Настройка фильтров

```python
# Включение регулярных выражений
"use_regex": true

# Учет регистра
"case_sensitive": true
```

### Настройка логирования

```json
"logging": {
  "level": "INFO",
  "file": "bot.log",
  "max_size_mb": 10,
  "backup_count": 5
}
```

## 🚨 Устранение неполадок

### Бот не запускается
1. Проверьте правильность токена бота
2. Убедитесь, что Chat ID указан корректно
3. Проверьте установку всех зависимостей

### Не приходят уведомления
1. Проверьте настройки фильтров
2. Убедитесь, что платформы включены
3. Проверьте логи на наличие ошибок

### Ошибки парсинга
1. Некоторые сайты могут блокировать запросы
2. Проверьте интернет-соединение
3. Увеличьте интервал проверки

## 📝 Лицензия

MIT License

## 🤝 Поддержка

Если у вас возникли вопросы или проблемы:
1. Проверьте логи бота
2. Убедитесь в правильности конфигурации
3. Создайте issue в репозитории

## 🔄 Обновления

Бот автоматически создает резервные копии конфигурации и базы данных. При обновлении:
1. Остановите бота
2. Обновите код
3. Запустите бота снова

---

**Удачного фриланса! 🚀**
