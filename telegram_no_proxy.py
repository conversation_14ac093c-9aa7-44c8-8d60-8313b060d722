#!/usr/bin/env python3
"""
Модуль для работы с Telegram без прокси
"""

import os
import sys
import subprocess

def disable_proxy_and_run_bot():
    """Отключает прокси и запускает бота"""
    print("🔧 Отключение прокси и запуск бота...")
    
    # Сохраняем текущие прокси
    old_proxies = {}
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    
    for var in proxy_vars:
        if var in os.environ:
            old_proxies[var] = os.environ[var]
    
    try:
        # Отключаем все прокси
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]
        
        print("✅ Прокси отключены")
        
        # Запускаем основной скрипт бота
        result = subprocess.run([sys.executable, "main.py"], 
                              env=os.environ.copy(),
                              cwd=os.getcwd())
        
        return result.returncode
        
    finally:
        # Восстанавливаем прокси
        for var, value in old_proxies.items():
            os.environ[var] = value
        print("🔄 Прокси восстановлены")

def test_telegram_connection():
    """Тестирует подключение к Telegram без прокси"""
    print("📱 Тестирование подключения к Telegram без прокси...")
    
    # Сохраняем текущие прокси
    old_proxies = {}
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    
    for var in proxy_vars:
        if var in os.environ:
            old_proxies[var] = os.environ[var]
    
    try:
        # Отключаем все прокси
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]
        
        import telebot
        from config import Config
        
        config = Config()
        
        # Создаем бота
        bot = telebot.TeleBot(config.telegram_token)
        
        # Пытаемся получить информацию о боте
        bot_info = bot.get_me()
        print(f"✅ Подключение к Telegram успешно!")
        print(f"  - Имя бота: {bot_info.first_name}")
        print(f"  - Username: @{bot_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к Telegram: {e}")
        return False
        
    finally:
        # Восстанавливаем прокси
        for var, value in old_proxies.items():
            os.environ[var] = value

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Режим тестирования
        success = test_telegram_connection()
        if success:
            print("\n🎉 Telegram работает! Можно запускать бота.")
        else:
            print("\n⚠️ Есть проблемы с Telegram.")
    else:
        # Режим запуска бота
        exit_code = disable_proxy_and_run_bot()
        sys.exit(exit_code)
