#!/usr/bin/env python3
"""
Скрипт для тестирования диагностики платформ
"""

import asyncio
import sys
import os

# Добавляем текущую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from logger import BotLogger
from parsers import ParserManager

async def main():
    """Основная функция тестирования"""
    print("🔍 Запуск диагностики платформ...")
    
    # Инициализируем компоненты
    config = Config()
    logger = BotLogger(config)
    parser_manager = ParserManager(config, logger)
    
    try:
        # Запускаем диагностику
        results = await parser_manager.diagnose_platforms()
        
        # Выводим результаты
        print("\n📊 Результаты диагностики:\n")
        
        working_count = 0
        total_count = len(results)
        
        for platform, result in results.items():
            if result.get('available', False):
                status = "✅ Работает"
                working_count += 1
                if result.get('has_projects', False):
                    status += f" (найдены проекты, {result.get('content_length', 0)} байт)"
                else:
                    status += f" (проекты не найдены, {result.get('content_length', 0)} байт)"
            else:
                status = "❌ Недоступна"
                if result.get('error'):
                    status += f" - {result['error']}"
            
            print(f"• {platform}: {status}")
        
        print(f"\n📈 Итого: {working_count}/{total_count} платформ работают")
        
        # Рекомендации
        if working_count < total_count:
            print("\n💡 Рекомендации:")
            for platform, result in results.items():
                if not result.get('available', False):
                    error = result.get('error', 'Неизвестная ошибка')
                    if 'HTTP 403' in error:
                        print(f"  - {platform}: Возможно блокировка по User-Agent или IP")
                    elif 'HTTP 410' in error:
                        print(f"  - {platform}: Сайт изменил структуру или URL")
                    elif 'HTTP 404' in error:
                        print(f"  - {platform}: Неверный URL или страница не существует")
                    elif 'timeout' in error.lower():
                        print(f"  - {platform}: Проблемы с сетью или медленный ответ")
                    else:
                        print(f"  - {platform}: {error}")
        
    except Exception as e:
        print(f"❌ Ошибка при диагностике: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
