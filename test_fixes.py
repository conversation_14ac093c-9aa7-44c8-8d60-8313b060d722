#!/usr/bin/env python3
"""
Скрипт для тестирования исправлений
"""

import asyncio
import sys
import os

# Добавляем текущую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from logger import BotLogger
from parsers import ParserManager
from database import Database

async def test_database_fixes():
    """Тестирует исправления базы данных"""
    print("🔧 Тестирование исправлений базы данных...")
    
    config = Config()
    logger = BotLogger(config)
    database = Database(config.database_file)
    
    # Создаем тестовые проекты с одинаковыми ID
    test_projects = [
        {
            'id': '123',
            'platform': 'FL.ru',
            'title': 'Тестовый проект 1',
            'description': 'Описание 1',
            'budget': 1000,
            'url': 'https://fl.ru/project/123'
        },
        {
            'id': '123',  # Тот же ID, но другая платформа
            'platform': 'Freelance.ru',
            'title': 'Тестовый проект 2',
            'description': 'Описание 2',
            'budget': 2000,
            'url': 'https://freelance.ru/project/123'
        },
        {
            'platform': 'Weblancer',  # Без ID - должен использовать хеш
            'title': 'Тестовый проект 3',
            'description': 'Описание 3',
            'budget': 3000,
            'url': 'https://weblancer.net/project/456'
        }
    ]
    
    # Пытаемся добавить проекты
    for i, project in enumerate(test_projects, 1):
        try:
            result = database.add_project(project)
            if result:
                print(f"✅ Проект {i} добавлен успешно")
            else:
                print(f"ℹ️ Проект {i} уже существует")
        except Exception as e:
            print(f"❌ Ошибка при добавлении проекта {i}: {e}")
    
    # Проверяем количество проектов в базе
    with database.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM projects")
        count = cursor.fetchone()[0]
        print(f"📊 Всего проектов в базе: {count}")
        
        # Показываем все проекты
        cursor.execute("SELECT id, platform, title FROM projects")
        projects = cursor.fetchall()
        for project in projects:
            print(f"  - ID: {project[0]}, Платформа: {project[1]}, Название: {project[2]}")

async def test_telegram_connection():
    """Тестирует подключение к Telegram"""
    print("\n📱 Тестирование подключения к Telegram...")
    
    try:
        import telebot
        import os

        config = Config()

        # Отключаем прокси через переменные окружения
        old_http_proxy = os.environ.get('HTTP_PROXY')
        old_https_proxy = os.environ.get('HTTPS_PROXY')
        old_http_proxy_lower = os.environ.get('http_proxy')
        old_https_proxy_lower = os.environ.get('https_proxy')

        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)

        try:
            # Создаем бота
            bot = telebot.TeleBot(config.telegram_token)
        finally:
            # Восстанавливаем прокси если они были
            if old_http_proxy:
                os.environ['HTTP_PROXY'] = old_http_proxy
            if old_https_proxy:
                os.environ['HTTPS_PROXY'] = old_https_proxy
            if old_http_proxy_lower:
                os.environ['http_proxy'] = old_http_proxy_lower
            if old_https_proxy_lower:
                os.environ['https_proxy'] = old_https_proxy_lower
        
        # Пытаемся получить информацию о боте
        bot_info = bot.get_me()
        print(f"✅ Подключение к Telegram успешно!")
        print(f"  - Имя бота: {bot_info.first_name}")
        print(f"  - Username: @{bot_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к Telegram: {e}")
        return False

async def main():
    """Основная функция тестирования"""
    print("🧪 Запуск тестирования исправлений...\n")
    
    # Тестируем базу данных
    await test_database_fixes()
    
    # Тестируем Telegram
    telegram_ok = await test_telegram_connection()
    
    print(f"\n📋 Результаты тестирования:")
    print(f"  - База данных: ✅ Исправлена")
    print(f"  - Telegram: {'✅ Работает' if telegram_ok else '❌ Проблемы'}")
    
    if telegram_ok:
        print(f"\n🎉 Все исправления работают! Можно запускать бота.")
    else:
        print(f"\n⚠️ Есть проблемы с Telegram. Проверьте токен и интернет-соединение.")

if __name__ == "__main__":
    asyncio.run(main())
