import logging
import logging.handlers
import os
from datetime import datetime

def setup_logger(name: str = 'freelance_bot', 
                log_file: str = 'bot.log', 
                level: str = 'INFO',
                max_size_mb: int = 10,
                backup_count: int = 5) -> logging.Logger:
    """
    Настраивает логгер с ротацией файлов
    
    Args:
        name: Имя логгера
        log_file: Путь к файлу логов
        level: Уровень логирования
        max_size_mb: Максимальный размер файла в МБ
        backup_count: Количество резервных файлов
    
    Returns:
        Настроенный логгер
    """
    
    # Создаем директорию для логов если её нет
    log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else '.'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Создаем логгер
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Очищаем существующие обработчики
    logger.handlers.clear()
    
    # Форматтер для логов
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Обработчик для файла с ротацией
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Обработчик для консоли
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

class BotLogger:
    """Класс для логирования событий бота"""
    
    def __init__(self, config):
        self.config = config
        self.logger = setup_logger(
            name='freelance_bot',
            log_file=config.log_file,
            level=config.log_level,
            max_size_mb=config.get('logging.max_size_mb', 10),
            backup_count=config.get('logging.backup_count', 5)
        )
    
    def info(self, message: str):
        """Логирует информационное сообщение"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Логирует предупреждение"""
        self.logger.warning(message)
    
    def error(self, message: str, exc_info=None):
        """Логирует ошибку"""
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str):
        """Логирует отладочное сообщение"""
        self.logger.debug(message)
    
    def log_project_found(self, project: dict, platform: str):
        """Логирует найденный проект"""
        self.info(f"Найден проект на {platform}: {project.get('title', 'Без названия')} "
                 f"(Бюджет: {project.get('budget', 0)} руб.)")
    
    def log_project_sent(self, project: dict):
        """Логирует отправленное уведомление"""
        self.info(f"Отправлено уведомление о проекте: {project.get('title', 'Без названия')}")
    
    def log_parsing_error(self, platform: str, error: Exception):
        """Логирует ошибку парсинга"""
        self.error(f"Ошибка при парсинге {platform}: {str(error)}", exc_info=True)
    
    def log_filter_match(self, project: dict, reason: str):
        """Логирует срабатывание фильтра"""
        self.debug(f"Проект '{project.get('title', 'Без названия')}' прошел фильтр: {reason}")
    
    def log_filter_reject(self, project: dict, reason: str):
        """Логирует отклонение фильтром"""
        self.debug(f"Проект '{project.get('title', 'Без названия')}' отклонен фильтром: {reason}")
    
    def log_bot_start(self):
        """Логирует запуск бота"""
        self.info("Бот запущен и готов к работе")
    
    def log_bot_stop(self):
        """Логирует остановку бота"""
        self.info("Бот остановлен")
    
    def log_config_change(self, key: str, old_value, new_value):
        """Логирует изменение конфигурации"""
        self.info(f"Изменена конфигурация {key}: {old_value} -> {new_value}")
    
    def log_platform_toggle(self, platform: str, enabled: bool):
        """Логирует включение/выключение платформы"""
        status = "включена" if enabled else "выключена"
        self.info(f"Платформа {platform} {status}")
    
    def log_monitoring_cycle_start(self):
        """Логирует начало цикла мониторинга"""
        self.debug("Начат цикл мониторинга платформ")
    
    def log_monitoring_cycle_end(self, projects_found: int, notifications_sent: int):
        """Логирует окончание цикла мониторинга"""
        self.info(f"Цикл мониторинга завершен. Найдено проектов: {projects_found}, "
                 f"отправлено уведомлений: {notifications_sent}")
    
    def log_database_operation(self, operation: str, details: str = ""):
        """Логирует операции с базой данных"""
        self.debug(f"База данных - {operation}: {details}")
    
    def log_telegram_error(self, error: Exception):
        """Логирует ошибки Telegram API"""
        self.error(f"Ошибка Telegram API: {str(error)}", exc_info=True)
    
    def log_network_error(self, url: str, error: Exception):
        """Логирует сетевые ошибки"""
        self.warning(f"Сетевая ошибка при обращении к {url}: {str(error)}")
    
    def log_rate_limit(self, platform: str, delay: int):
        """Логирует ограничение скорости запросов"""
        self.warning(f"Ограничение скорости для {platform}, ожидание {delay} секунд")
    
    def log_summary(self, summary_data: dict):
        """Логирует сводку работы"""
        self.info(f"Сводка за период: {summary_data}")
